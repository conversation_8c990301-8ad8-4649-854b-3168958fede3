const Tournament = require('../models/Tournament');
const Team = require('../models/Team');
const User = require('../models/User');
const mongoose = require('mongoose');

/**
 * Get all tournaments with filtering options
 * @route GET /api/tournaments
 * @access Public
 */
exports.getAllTournaments = async (req, res) => {
  try {
    const { page = 1, limit = 10, status, format, search } = req.query;

    // Build query
    const query = {};

    if (status) {
      query.status = status;
    }

    if (format) {
      query.format = format;
    }

    if (search) {
      query.name = { $regex: search, $options: 'i' };
    }

    // Count total tournaments matching query
    const total = await Tournament.countDocuments(query);

    // Get paginated tournaments
    const tournaments = await Tournament.find(query)
      .populate('createdBy', 'username')
      .populate('registeredTeams', 'teamName logo')
      .sort({ startDate: 1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    res.json({
      success: true,
      data: tournaments,
      pagination: {
        total,
        page: parseInt(page),
        pages: Math.ceil(total / limit)
      }
    });
  } catch (err) {
    console.error('Error getting tournaments:', err);
    res.status(500).json({ msg: 'Server error' });
  }
};

/**
 * Get tournament by ID
 * @route GET /api/tournaments/:id
 * @access Public
 */
exports.getTournamentById = async (req, res) => {
  try {
    const tournament = await Tournament.findById(req.params.id)
      .populate('createdBy', 'username')
      .populate('registeredTeams', 'teamName logo owner')
      .populate({
        path: 'phases.matches.homeTeam',
        select: 'teamName logo'
      })
      .populate({
        path: 'phases.matches.awayTeam',
        select: 'teamName logo'
      })
      .populate({
        path: 'phases.matches.result.winner',
        select: 'teamName logo'
      })
      .populate({
        path: 'phases.standings.team',
        select: 'teamName logo'
      });

    if (!tournament) {
      return res.status(404).json({ msg: 'Tournament not found' });
    }

    res.json(tournament);
  } catch (err) {
    console.error('Error getting tournament:', err);
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'Tournament not found' });
    }
    res.status(500).json({ msg: 'Server error' });
  }
};

/**
 * Create a new tournament
 * @route POST /api/tournaments
 * @access Private (Admin only)
 */
exports.createTournament = async (req, res) => {
  try {
    const {
      name,
      description,
      format,
      startDate,
      endDate,
      registrationDeadline,
      maxTeams,
      pointsForWin,
      pointsForTie,
      pointsForNoResult,
      roundConfiguration,
      logo,
      banner,
      primaryColor,
      secondaryColor
    } = req.body;

    // Check if tournament with same name already exists
    const existingTournament = await Tournament.findOne({ name });
    if (existingTournament) {
      return res.status(400).json({ msg: 'Tournament with this name already exists' });
    }

    // Create new tournament
    const tournament = new Tournament({
      name,
      description,
      format,
      startDate,
      endDate,
      registrationDeadline,
      maxTeams,
      pointsForWin: pointsForWin || 2,
      pointsForTie: pointsForTie || 1,
      pointsForNoResult: pointsForNoResult || 1,
      roundConfiguration: roundConfiguration || {
        groupStageRounds: 1,
        knockoutFormat: 'single-elimination',
        thirdPlaceMatch: false,
        finalFormat: 'single-match'
      },
      logo: logo || '/uploads/tournaments/default.png',
      banner: banner || '/uploads/tournaments/default-banner.png',
      primaryColor: primaryColor || '#1e88e5',
      secondaryColor: secondaryColor || '#bbdefb',
      createdBy: req.user.id
    });

    await tournament.save();
    res.json(tournament);
  } catch (err) {
    console.error('Error creating tournament:', err);
    res.status(500).json({ msg: 'Server error' });
  }
};

/**
 * Update tournament
 * @route PUT /api/tournaments/:id
 * @access Private (Admin only)
 */
exports.updateTournament = async (req, res) => {
  try {
    const {
      name,
      description,
      format,
      startDate,
      endDate,
      registrationDeadline,
      maxTeams,
      status,
      pointsForWin,
      pointsForTie,
      pointsForNoResult,
      roundConfiguration,
      logo,
      banner,
      primaryColor,
      secondaryColor
    } = req.body;

    // Find tournament
    const tournament = await Tournament.findById(req.params.id);
    if (!tournament) {
      return res.status(404).json({ msg: 'Tournament not found' });
    }

    // Check if tournament with same name already exists (excluding this one)
    if (name && name !== tournament.name) {
      const existingTournament = await Tournament.findOne({ name });
      if (existingTournament) {
        return res.status(400).json({ msg: 'Tournament with this name already exists' });
      }
    }

    // Update fields
    if (name) tournament.name = name;
    if (description !== undefined) tournament.description = description;
    if (format) tournament.format = format;
    if (startDate) tournament.startDate = startDate;
    if (endDate) tournament.endDate = endDate;
    if (registrationDeadline) tournament.registrationDeadline = registrationDeadline;
    if (maxTeams) tournament.maxTeams = maxTeams;
    if (status) tournament.status = status;
    if (pointsForWin) tournament.pointsForWin = pointsForWin;
    if (pointsForTie) tournament.pointsForTie = pointsForTie;
    if (pointsForNoResult) tournament.pointsForNoResult = pointsForNoResult;
    if (roundConfiguration) tournament.roundConfiguration = roundConfiguration;
    if (logo) tournament.logo = logo;
    if (banner) tournament.banner = banner;
    if (primaryColor) tournament.primaryColor = primaryColor;
    if (secondaryColor) tournament.secondaryColor = secondaryColor;

    await tournament.save();
    res.json(tournament);
  } catch (err) {
    console.error('Error updating tournament:', err);
    res.status(500).json({ msg: 'Server error' });
  }
};

/**
 * Delete tournament
 * @route DELETE /api/tournaments/:id
 * @access Private (Admin only)
 */
exports.deleteTournament = async (req, res) => {
  try {
    const tournament = await Tournament.findById(req.params.id);
    if (!tournament) {
      return res.status(404).json({ msg: 'Tournament not found' });
    }

    await tournament.remove();
    res.json({ msg: 'Tournament removed' });
  } catch (err) {
    console.error('Error deleting tournament:', err);
    res.status(500).json({ msg: 'Server error' });
  }
};

/**
 * Register team for tournament
 * @route POST /api/tournaments/:id/register
 * @access Private (Team Owner only)
 */
exports.registerTeam = async (req, res) => {
  try {
    // Find tournament
    const tournament = await Tournament.findById(req.params.id);
    if (!tournament) {
      return res.status(404).json({ msg: 'Tournament not found' });
    }

    // Check if tournament is in registration phase
    if (tournament.status !== 'registration') {
      return res.status(400).json({ msg: 'Tournament registration is closed' });
    }

    // Check if tournament has reached max teams
    if (tournament.registeredTeams.length >= tournament.maxTeams) {
      return res.status(400).json({ msg: 'Tournament has reached maximum number of teams' });
    }

    // Get user's team
    const user = await User.findById(req.user.id).populate('team');
    if (!user.team) {
      return res.status(400).json({ msg: 'You need to create a team first' });
    }

    // Check if team is already registered
    if (tournament.registeredTeams.includes(user.team._id)) {
      return res.status(400).json({ msg: 'Your team is already registered for this tournament' });
    }

    // Add team to tournament
    tournament.registeredTeams.push(user.team._id);
    await tournament.save();

    res.json({ msg: 'Team registered successfully', tournament });
  } catch (err) {
    console.error('Error registering team:', err);
    res.status(500).json({ msg: 'Server error' });
  }
};

/**
 * Unregister team from tournament
 * @route POST /api/tournaments/:id/unregister
 * @access Private (Team Owner only)
 */
exports.unregisterTeam = async (req, res) => {
  try {
    // Find tournament
    const tournament = await Tournament.findById(req.params.id);
    if (!tournament) {
      return res.status(404).json({ msg: 'Tournament not found' });
    }

    // Check if tournament is in registration phase
    if (tournament.status !== 'registration') {
      return res.status(400).json({ msg: 'Tournament registration is closed' });
    }

    // Get user's team
    const user = await User.findById(req.user.id).populate('team');
    if (!user.team) {
      return res.status(400).json({ msg: 'You do not have a team' });
    }

    // Check if team is registered
    if (!tournament.registeredTeams.includes(user.team._id)) {
      return res.status(400).json({ msg: 'Your team is not registered for this tournament' });
    }

    // Remove team from tournament
    tournament.registeredTeams = tournament.registeredTeams.filter(
      teamId => teamId.toString() !== user.team._id.toString()
    );
    await tournament.save();

    res.json({ msg: 'Team unregistered successfully', tournament });
  } catch (err) {
    console.error('Error unregistering team:', err);
    res.status(500).json({ msg: 'Server error' });
  }
};

/**
 * Get next round number for a match between two teams
 * @route GET /api/tournaments/:id/next-round/:team1Id/:team2Id
 * @access Private (Team Owner only)
 */
exports.getNextRoundNumber = async (req, res) => {
  try {
    const { id, team1Id, team2Id } = req.params;

    // Find tournament
    const tournament = await Tournament.findById(id);
    if (!tournament) {
      return res.status(404).json({ msg: 'Tournament not found' });
    }

    // Get next round number
    const nextRound = tournament.getNextRoundNumber(team1Id, team2Id);

    // Return the next round number and completed rounds
    res.json({
      nextRound,
      completedRounds: nextRound - 1
    });
  } catch (err) {
    console.error('Error getting next round number:', err);
    res.status(500).json({ msg: 'Server error' });
  }
};

/**
 * Add a match result
 * @route POST /api/tournaments/:id/matches
 * @access Private (Team Owner only)
 */
exports.addMatch = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      opponentTeamId,
      isHomeMatch,
      date,
      venue,
      homeTeamBattedFirst,
      result,
      scorecardImage,
      ocrData
    } = req.body;

    // Debug logging for scorecard image
    console.log('🔍 SERVER addMatch received data:', {
      scorecardImage: scorecardImage ? 'Present' : 'Not present',
      scorecardImageLength: scorecardImage ? scorecardImage.length : 0,
      ocrData: ocrData ? 'Present' : 'Not present',
      resultScorecardImages: result?.scorecardImages?.length || 0,
      'result.scorecardImages': result?.scorecardImages
    });

    console.log('🔍 SERVER Full result object:', result);

    // Debug: Check if we have a scorecardImage to add to result.scorecardImages
    if (scorecardImage) {
      console.log('🔍 SERVER: scorecardImage provided, will add to result.scorecardImages');
      console.log('🔍 SERVER: scorecardImage URL:', scorecardImage);
    }

    // Find tournament
    const tournament = await Tournament.findById(id);
    if (!tournament) {
      return res.status(404).json({ msg: 'Tournament not found' });
    }

    // Check if tournament is in progress
    if (tournament.status !== 'in_progress') {
      return res.status(400).json({ msg: 'Tournament is not in progress' });
    }

    // Get user's team
    const user = await User.findById(req.user.id).populate('team');
    if (!user.team) {
      return res.status(400).json({ msg: 'You do not have a team' });
    }

    // Check if both teams are registered for the tournament
    if (!tournament.registeredTeams.includes(user.team._id)) {
      return res.status(400).json({ msg: 'Your team is not registered for this tournament' });
    }
    if (!tournament.registeredTeams.includes(opponentTeamId)) {
      return res.status(400).json({ msg: 'Opponent team is not registered for this tournament' });
    }

    // Determine home and away teams
    const homeTeamId = isHomeMatch ? user.team._id : opponentTeamId;
    const awayTeamId = isHomeMatch ? opponentTeamId : user.team._id;

    // Get next round number
    const roundNumber = tournament.getNextRoundNumber(user.team._id, opponentTeamId);

    // Create match object
    const match = {
      homeTeam: homeTeamId,
      awayTeam: awayTeamId,
      date: date || new Date(),
      venue: venue || 'Default Venue',
      format: tournament.format,
      status: 'completed',
      homeTeamBattedFirst: req.body.homeTeamBattedFirst === undefined ? true : req.body.homeTeamBattedFirst, // Ensure default value if undefined
      team1IsHomeTeam: isHomeMatch, // Add flag to track if team1 from OCR is the home team
      result: {
        winner: result.winnerId && result.winnerId.trim() !== '' ? result.winnerId : null,
        homeTeamScore: {
          runs: isHomeMatch ? result.team1Score?.runs || 0 : result.team2Score?.runs || 0,
          wickets: isHomeMatch ? result.team1Score?.wickets || 0 : result.team2Score?.wickets || 0,
          overs: isHomeMatch ? result.team1Score?.overs || 0 : result.team2Score?.overs || 0,
          extras: isHomeMatch ? result.team1Score?.extras || 0 : result.team2Score?.extras || 0,
          ballsFaced: isHomeMatch ? result.team1Score?.ballsFaced || 0 : result.team2Score?.ballsFaced || 0
        },
        awayTeamScore: {
          runs: isHomeMatch ? result.team2Score?.runs || 0 : result.team1Score?.runs || 0,
          wickets: isHomeMatch ? result.team2Score?.wickets || 0 : result.team1Score?.wickets || 0,
          overs: isHomeMatch ? result.team2Score?.overs || 0 : result.team1Score?.overs || 0,
          extras: isHomeMatch ? result.team2Score?.extras || 0 : result.team1Score?.extras || 0,
          ballsFaced: isHomeMatch ? result.team2Score?.ballsFaced || 0 : result.team1Score?.ballsFaced || 0
        },
        isTie: result.isTie || false,
        verificationStatus: 'pending',
        // Include scorecard images if provided
        scorecardImages: (() => {
          const images = result.scorecardImages || [];

          // If a scorecardImage is provided at the top level, add it to the result.scorecardImages array
          if (scorecardImage && !images.some(img => img.url === scorecardImage)) {
            images.push({
              url: scorecardImage,
              uploadedBy: req.user.id,
              uploadedAt: new Date(),
              isVerified: false
            });
          }

          return images;
        })()
      },
      // Store the scorecard image at the match level if provided
      ...(scorecardImage && { scorecardImage }),
      // Store OCR data if provided
      ...(ocrData && { ocrData }),
      conditions: {
        weather: 'Sunny',
        pitch: 'Balanced',
        outfield: 'Average'
      }
    };
    
    // Log match creation details for debugging
    console.log('Creating match with batting order flags:', {
      homeTeamBattedFirst: match.homeTeamBattedFirst,
      team1IsHomeTeam: match.team1IsHomeTeam,
      homeTeamId: homeTeamId,
      awayTeamId: awayTeamId
    });

    // Debug: Log the final scorecardImages in the match result
    console.log('🔍 SERVER: Final match.result.scorecardImages:', {
      count: match.result.scorecardImages?.length || 0,
      images: match.result.scorecardImages
    });

    // Find or create league phase
    let leaguePhase = tournament.phases.find(phase => phase.name === 'League Phase');
    if (!leaguePhase) {
      leaguePhase = {
        name: 'League Phase',
        type: 'group',
        startDate: tournament.startDate,
        endDate: tournament.endDate,
        matches: [],
        standings: []
      };
      tournament.phases.push(leaguePhase);
    }

    // Add match to league phase
    leaguePhase.matches.push(match);

    // Update standings
    const updateStandings = (teamId, isWinner) => {
      let standing = leaguePhase.standings.find(s => s.team.toString() === teamId.toString());

      if (!standing) {
        standing = {
          team: teamId,
          played: 0,
          won: 0,
          lost: 0,
          tied: 0,
          noResult: 0,
          points: 0,
          netRunRate: 0
        };
        leaguePhase.standings.push(standing);
      }

      standing.played += 1;

      if (result.winnerId) {
        if (teamId.toString() === result.winnerId.toString()) {
          standing.won += 1;
          standing.points += tournament.pointsForWin;
        } else {
          standing.lost += 1;
        }
      } else {
        // No winner (tie or no result)
        if (result.isTie) {
          standing.tied += 1;
          standing.points += tournament.pointsForTie;
        } else {
          standing.noResult += 1;
          standing.points += tournament.pointsForNoResult;
        }
      }
    };

    // Update standings for both teams
    updateStandings(homeTeamId, homeTeamId.toString() === result.winnerId?.toString());
    updateStandings(awayTeamId, awayTeamId.toString() === result.winnerId?.toString());

    // Save tournament
    const savedTournament = await tournament.save();

    // Get the newly created match ID from the saved tournament
    const savedLeaguePhase = savedTournament.phases.find(phase => phase.name === 'League Phase');
    const newMatch = savedLeaguePhase.matches[savedLeaguePhase.matches.length - 1];
    const matchId = newMatch._id;

    res.json({
      msg: 'Match added successfully',
      match: newMatch,
      matchId: matchId,
      roundNumber
    });
  } catch (err) {
    console.error('Error adding match:', err);
    res.status(500).json({ msg: 'Server error' });
  }
};

/**
 * Get matches between two teams
 * @route GET /api/tournaments/:id/matches/:team1Id/:team2Id
 * @access Private (Team Owner only)
 */
exports.getMatchesBetweenTeams = async (req, res) => {
  try {
    const { id, team1Id, team2Id } = req.params;

    // Find tournament
    const tournament = await Tournament.findById(id);
    if (!tournament) {
      return res.status(404).json({ msg: 'Tournament not found' });
    }

    // Find all matches between these two teams
    let matchesBetweenTeams = [];

    // Look through all phases for matches between these teams
    if (tournament.phases && tournament.phases.length > 0) {
      tournament.phases.forEach(phase => {
        if (phase.matches && phase.matches.length > 0) {
          const teamMatches = phase.matches.filter(match =>
            (match.homeTeam.toString() === team1Id.toString() && match.awayTeam.toString() === team2Id.toString()) ||
            (match.homeTeam.toString() === team2Id.toString() && match.awayTeam.toString() === team1Id.toString())
          );
          matchesBetweenTeams = [...matchesBetweenTeams, ...teamMatches];
        }
      });
    }

    res.json({
      matches: matchesBetweenTeams,
      completedRounds: matchesBetweenTeams.length
    });
  } catch (err) {
    console.error('Error getting matches between teams:', err);
    res.status(500).json({ msg: 'Server error' });
  }
};
