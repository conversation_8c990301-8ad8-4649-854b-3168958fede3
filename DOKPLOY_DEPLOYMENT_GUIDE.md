# 🚀 Dokploy Deployment Guide for RPL Cricket App

## 🚨 Critical Issues to Fix First

### 1. **Disk Space Issue (URGENT)**
Your Dokploy server has run out of disk space. SSH into your server and run:

```bash
# Clean up Docker resources
docker system prune -a --volumes
docker builder prune -a

# Check disk space
df -h

# Clean up old deployments if needed
docker images | grep rpl-frontend | awk '{print $3}' | xargs docker rmi -f
```

### 2. **Repository Structure Issue**
The current deployment is trying to deploy the `client` folder as a standalone app, but it's part of a monorepo.

## 🔧 Deployment Configuration

### Option 1: Deploy Frontend Only (Recommended for now)

1. **Update Dokploy Application Settings:**
   - **Source Directory**: `client`
   - **Build Command**: `npm ci && npm run build`
   - **Start Command**: `npx serve -s build -l $PORT`

2. **Environment Variables:**
   ```
   NODE_ENV=production
   PORT=3000
   REACT_APP_API_URL=https://your-backend-url.com/api
   ```

### Option 2: Deploy Full Stack Application

1. **Create separate Dokploy applications:**
   - **Frontend**: Deploy from `client` folder
   - **Backend**: Deploy from `server` folder

2. **Backend Configuration:**
   - **Source Directory**: `server`
   - **Build Command**: `npm ci`
   - **Start Command**: `npm start`
   - **Environment Variables**:
     ```
     NODE_ENV=production
     PORT=5000
     MONGODB_URI=your-mongodb-connection-string
     JWT_SECRET=your-jwt-secret
     ```

## 📝 Files Created for Deployment

1. **`Procfile`** - Defines how to start the application
2. **`dokploy.json`** - Dokploy configuration
3. **`.dokploy/build.sh`** - Custom build script
4. **`.dokploy/start.sh`** - Custom start script
5. **Updated `client/package.json`** - Added `serve` dependency

## 🔍 Troubleshooting

### Common Issues:

1. **Build Fails**: Check if all dependencies are in package.json
2. **App Won't Start**: Verify PORT environment variable
3. **API Calls Fail**: Update REACT_APP_API_URL environment variable

### ESLint Warnings:
The build shows many ESLint warnings but they don't prevent deployment. To fix them:
- Remove unused imports
- Add missing dependencies to useEffect hooks
- Fix mixed operators with parentheses

## 🎯 Next Steps

1. **Fix disk space** on Dokploy server
2. **Update environment variables** in Dokploy dashboard
3. **Redeploy** the application
4. **Test** the deployed application
5. **Set up backend** deployment if needed

## 📞 Support

If you encounter issues:
1. Check Dokploy logs for specific errors
2. Verify environment variables are set correctly
3. Ensure the build process completes successfully
4. Test locally with `npm run build && npx serve -s build`
