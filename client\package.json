{"name": "client", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.0.2", "@mui/material": "^7.0.2", "@mui/x-date-pickers": "^8.3.0", "@tanstack/react-query": "^5.83.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.9.0", "date-fns": "^4.1.0", "framer-motion": "^12.10.0", "notistack": "^3.0.2", "react": "^19.1.0", "react-country-flag": "^3.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.5.3", "react-scripts": "5.0.1", "socket.io-client": "^4.8.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "cross-env PORT=4000 react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000", "devDependencies": {"cross-env": "^7.0.3", "typescript": "^4.9.5"}}