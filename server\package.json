{"name": "server", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "debug": "node --trace-warnings --trace-uncaught --inspect index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@google-cloud/vision": "^5.1.0", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "cheerio": "^1.0.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "form-data": "^4.0.2", "jsonwebtoken": "^9.0.2", "mongodb": "^6.16.0", "mongoose": "^8.14.1", "multer": "^1.4.5-lts.2", "node-fetch": "^2.7.0", "puppeteer": "^24.8.0", "serve": "^14.2.4", "sharp": "^0.34.2", "uuid": "^11.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"nodemon": "^3.1.10"}}