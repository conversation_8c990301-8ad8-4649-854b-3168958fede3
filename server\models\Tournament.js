const mongoose = require('mongoose');

// Define match schema for tournament matches
const matchSchema = new mongoose.Schema({
  homeTeam: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Team',
    required: true
  },
  awayTeam: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Team',
    required: true
  },
  date: {
    type: Date,
    required: true
  },
  venue: {
    type: String,
    default: 'TBD'
  },
  format: {
    type: String,
    enum: ['T10', 'T20', 'ODI', 'Test'],
    required: true
  },
  status: {
    type: String,
    enum: ['scheduled', 'in_progress', 'completed', 'cancelled'],
    default: 'scheduled'
  },
  homeTeamBattedFirst: {
    type: <PERSON>olean,
    default: true,
    description: 'Indicates whether the home team batted first (true) or chased (false)'
  },
  team1IsHomeTeam: {
    type: Boolean,
    default: true,
    description: 'Indicates whether team1 from OCR data is the home team (true) or away team (false)'
  },
  result: {
    winner: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Team',
      default: null
    },
    homeTeamScore: {
      runs: { type: Number, default: 0 },
      wickets: { type: Number, default: 0 },
      overs: { type: Number, default: 0 },
      extras: { type: Number, default: 0 },
      ballsFaced: { type: Number, default: 0 }
    },
    awayTeamScore: {
      runs: { type: Number, default: 0 },
      wickets: { type: Number, default: 0 },
      overs: { type: Number, default: 0 },
      extras: { type: Number, default: 0 },
      ballsFaced: { type: Number, default: 0 }
    },
    manOfTheMatch: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Player',
      default: null
    },
    playerPerformances: [{
      player: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Player'
      },
      playerName: {
        type: String,
        required: true
      },
      team: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Team'
      },
      batting: {
        runs: { type: Number, default: 0 },
        ballsFaced: { type: Number, default: 0 },
        fours: { type: Number, default: 0 },
        sixes: { type: Number, default: 0 },
        notOut: { type: Boolean, default: false }
      },
      bowling: {
        overs: { type: Number, default: 0 },
        maidens: { type: Number, default: 0 },
        runs: { type: Number, default: 0 },
        wickets: { type: Number, default: 0 },
        economy: { type: Number, default: 0 }
      },
      fielding: {
        catches: { type: Number, default: 0 },
        runOuts: { type: Number, default: 0 },
        stumpings: { type: Number, default: 0 }
      }
    }],
    description: {
      type: String,
      default: ''
    },
    highlights: {
      type: String,
      default: ''
    },
    isTie: {
      type: Boolean,
      default: false
    },
    isDuckworthLewis: {
      type: Boolean,
      default: false
    },
    duckworthLewisTarget: {
      type: Number,
      default: 0
    },
    matchNotes: {
      type: String,
      default: ''
    },
    scorecardImages: [{
      url: { type: String },
      uploadedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      uploadedAt: {
        type: Date,
        default: Date.now
      },
      isVerified: {
        type: Boolean,
        default: false
      }
    }],
    verificationStatus: {
      type: String,
      enum: ['pending', 'verified', 'disputed', 'resolved'],
      default: 'pending'
    },
    verifiedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      default: null
    },
    verifiedAt: {
      type: Date,
      default: null
    },
    dispute: {
      isDisputed: {
        type: Boolean,
        default: false
      },
      disputedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        default: null
      },
      disputeReason: {
        type: String,
        default: ''
      },
      disputeStatus: {
        type: String,
        enum: ['open', 'resolved', 'rejected'],
        default: 'open'
      },
      disputeResolution: {
        type: String,
        default: ''
      }
    },
    extractedData: {
      isExtracted: {
        type: Boolean,
        default: false
      },
      extractionMethod: {
        type: String,
        enum: ['ocr', 'manual', 'none'],
        default: 'none'
      },
      extractedAt: {
        type: Date,
        default: null
      },
      rawData: {
        type: String,
        default: ''
      }
    }
  },
  conditions: {
    weather: {
      type: String,
      enum: ['Sunny', 'Cloudy', 'Rainy', 'Overcast', 'Hot', 'Humid'],
      default: 'Sunny'
    },
    pitch: {
      type: String,
      enum: ['Batting', 'Bowling', 'Balanced', 'Spinning', 'Green'],
      default: 'Balanced'
    },
    outfield: {
      type: String,
      enum: ['Fast', 'Slow', 'Average'],
      default: 'Average'
    }
  }
}, {
  timestamps: true
});

// Define team standings schema for tournament standings
const teamStandingSchema = new mongoose.Schema({
  team: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Team',
    required: true
  },
  played: {
    type: Number,
    default: 0
  },
  won: {
    type: Number,
    default: 0
  },
  lost: {
    type: Number,
    default: 0
  },
  tied: {
    type: Number,
    default: 0
  },
  noResult: {
    type: Number,
    default: 0
  },
  points: {
    type: Number,
    default: 0
  },
  netRunRate: {
    type: Number,
    default: 0
  },
  // Cumulative data for NRR calculation
  totalRunsScored: {
    type: Number,
    default: 0
  },
  totalOversPlayed: {
    type: Number,
    default: 0
  },
  totalRunsConceded: {
    type: Number,
    default: 0
  },
  totalOversBowled: {
    type: Number,
    default: 0
  }
});

// Define tournament phase schema
const phaseSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true
  },
  type: {
    type: String,
    enum: ['group', 'knockout', 'final'],
    required: true
  },
  startDate: {
    type: Date,
    required: true
  },
  endDate: {
    type: Date,
    required: true
  },
  matches: [matchSchema],
  standings: [teamStandingSchema]
});

// Define tournament schema
const tournamentSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    unique: true
  },
  description: {
    type: String,
    default: ''
  },
  format: {
    type: String,
    enum: ['T10', 'T20', 'ODI', 'Test'],
    required: true
  },
  startDate: {
    type: Date,
    required: true
  },
  endDate: {
    type: Date,
    required: true,
    validate: {
      validator: function(value) {
        return value > this.startDate;
      },
      message: 'End date must be after start date'
    }
  },
  registrationDeadline: {
    type: Date,
    required: true,
    validate: {
      validator: function(value) {
        return value < this.startDate;
      },
      message: 'Registration deadline must be before start date'
    }
  },
  status: {
    type: String,
    enum: ['registration', 'in_progress', 'completed', 'cancelled'],
    default: 'registration'
  },
  maxTeams: {
    type: Number,
    required: true,
    min: 2,
    max: 32
  },
  registeredTeams: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Team'
  }],
  phases: [phaseSchema],
  pointsForWin: {
    type: Number,
    default: 2
  },
  pointsForTie: {
    type: Number,
    default: 1
  },
  pointsForNoResult: {
    type: Number,
    default: 1
  },
  roundConfiguration: {
    groupStageRounds: {
      type: Number,
      default: 1,
      min: 1,
      max: 4,
      description: 'Number of times each team plays against others in group stage'
    },
    knockoutFormat: {
      type: String,
      enum: ['single-elimination', 'double-elimination', 'playoffs', 'ipl-style'],
      default: 'single-elimination'
    },
    playoffQualifiers: {
      type: Number,
      enum: [4, 8],
      default: 4,
      description: 'Number of teams qualifying for playoffs in IPL-style format'
    },
    useGroups: {
      type: Boolean,
      default: false,
      description: 'Whether to use groups in the league phase or have all teams play each other'
    },
    thirdPlaceMatch: {
      type: Boolean,
      default: false
    },
    finalFormat: {
      type: String,
      enum: ['single-match', 'best-of-three', 'best-of-five'],
      default: 'single-match'
    }
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  logo: {
    type: String,
    default: '/uploads/tournaments/default.png'
  },
  banner: {
    type: String,
    default: '/uploads/tournaments/default-banner.png'
  },
  primaryColor: {
    type: String,
    default: '#1e88e5' // Default blue
  },
  secondaryColor: {
    type: String,
    default: '#bbdefb' // Light blue
  }
}, {
  timestamps: true
});

// Pre-save middleware to update tournament status based on dates
tournamentSchema.pre('save', function(next) {
  const now = new Date();

  if (now < this.registrationDeadline) {
    this.status = 'registration';
  } else if (now >= this.startDate && now <= this.endDate) {
    this.status = 'in_progress';
  } else if (now > this.endDate) {
    this.status = 'completed';
  }

  next();
});

// Method to get the next round number for a match between two teams
tournamentSchema.methods.getNextRoundNumber = function(team1Id, team2Id) {
  // Find all matches between these two teams
  let matchesBetweenTeams = [];

  // Look through all phases for matches between these teams
  if (this.phases && this.phases.length > 0) {
    this.phases.forEach(phase => {
      if (phase.matches && phase.matches.length > 0) {
        const teamMatches = phase.matches.filter(match =>
          (match.homeTeam.toString() === team1Id.toString() && match.awayTeam.toString() === team2Id.toString()) ||
          (match.homeTeam.toString() === team2Id.toString() && match.awayTeam.toString() === team1Id.toString())
        );
        matchesBetweenTeams = [...matchesBetweenTeams, ...teamMatches];
      }
    });
  }

  // Return the next round number (1-based)
  return matchesBetweenTeams.length + 1;
};

const Tournament = mongoose.model('Tournament', tournamentSchema);

module.exports = Tournament;
