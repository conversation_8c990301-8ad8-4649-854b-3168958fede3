const mongoose = require('mongoose');
require('dotenv').config();

async function checkMatchDetails() {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/cricket24');
    console.log('Connected to database');
    
    // Find the tournament
    const Tournament = require('./models/Tournament');
    const tournament = await Tournament.findById('681e581dfe3bc0a0414dd937');
    
    if (!tournament) {
      console.log('Tournament not found');
      return;
    }
    
    // Find the latest match (last one in the array)
    let latestMatch = null;
    let latestDate = new Date(0); // Start with epoch
    
    for (const phase of tournament.phases) {
      for (const match of phase.matches) {
        if (match.createdAt && match.createdAt > latestDate) {
          latestMatch = match;
          latestDate = match.createdAt;
        }
      }
    }
    
    if (!latestMatch) {
      console.log('No matches found');
      return;
    }
    
    console.log('🔍 LATEST MATCH DETAILED ANALYSIS:');
    console.log('Match ID:', latestMatch._id);
    console.log('Created At:', latestMatch.createdAt);
    console.log('Status:', latestMatch.status);
    
    console.log('\n📊 COMPLETE MATCH RESULT OBJECT:');
    console.log(JSON.stringify(latestMatch.result, null, 2));
    
    console.log('\n🔍 CHECKING FOR PLAYER DATA:');
    console.log('playerPerformances:', latestMatch.result?.playerPerformances?.length || 0);
    console.log('team1Batsmen:', latestMatch.result?.team1Batsmen?.length || 0);
    console.log('team2Batsmen:', latestMatch.result?.team2Batsmen?.length || 0);
    console.log('team1Bowlers:', latestMatch.result?.team1Bowlers?.length || 0);
    console.log('team2Bowlers:', latestMatch.result?.team2Bowlers?.length || 0);
    
    console.log('\n🔍 CHECKING FOR OCR DATA:');
    console.log('match.ocrData present:', latestMatch.ocrData ? 'Yes' : 'No');
    if (latestMatch.ocrData) {
      console.log('ocrData keys:', Object.keys(latestMatch.ocrData));
    }
    
    console.log('\n🔍 CHECKING MATCH LEVEL FIELDS:');
    console.log('All match keys:', Object.keys(latestMatch.toObject()));
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

checkMatchDetails();
