const mongoose = require('mongoose');
require('dotenv').config();

async function testFix() {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/cricket24');
    console.log('Connected to database');
    
    // Find the tournament
    const Tournament = require('./models/Tournament');
    const tournament = await Tournament.findById('681e581dfe3bc0a0414dd937');
    
    if (!tournament) {
      console.log('Tournament not found');
      return;
    }
    
    // Find the specific match
    const matchId = '68787b242a02cd1cf7ccc915';
    let match = null;
    let phaseIndex = -1;
    let matchIndex = -1;
    
    for (let i = 0; i < tournament.phases.length; i++) {
      const foundMatchIndex = tournament.phases[i].matches.findIndex(m => m._id.toString() === matchId);
      if (foundMatchIndex !== -1) {
        match = tournament.phases[i].matches[foundMatchIndex];
        phaseIndex = i;
        matchIndex = foundMatchIndex;
        break;
      }
    }
    
    if (!match) {
      console.log('Match not found');
      return;
    }
    
    console.log('🔍 BEFORE TEST:');
    console.log('scorecardImages length:', match.result?.scorecardImages?.length || 0);
    
    // Manually add a scorecard image to test our fix
    console.log('\n📸 Adding test scorecard image...');
    if (!match.result.scorecardImages) {
      match.result.scorecardImages = [];
    }
    
    match.result.scorecardImages.push({
      url: '/uploads/scorecards/test-scorecard.jpg',
      uploadedBy: new mongoose.Types.ObjectId('6817724364f98b5cae6e57f1'),
      uploadedAt: new Date(),
      isVerified: false
    });
    
    console.log('scorecardImages after adding:', match.result.scorecardImages.length);
    
    // Save the tournament to persist the test image
    await tournament.save();
    console.log('✅ Test scorecard image saved to database');
    
    // Now simulate what the matchOutcomeService does (the OLD buggy way)
    console.log('\n🧪 TESTING OUR FIX:');
    console.log('Simulating matchOutcomeService update...');
    
    // This is what the OLD code did (overwrote the entire result object)
    const existingResult = tournament.phases[phaseIndex].matches[matchIndex].result || {};
    
    console.log('🔍 existingResult.scorecardImages:', existingResult.scorecardImages?.length || 0);
    
    // Apply our FIX - preserve existing data with spread operator
    tournament.phases[phaseIndex].matches[matchIndex].result = {
      ...existingResult, // This should preserve scorecardImages
      winner: match.result.winner,
      homeTeamScore: match.result.homeTeamScore,
      awayTeamScore: match.result.awayTeamScore,
      // ... other fields would be here in real scenario
    };
    
    console.log('🔍 AFTER FIX:');
    console.log('scorecardImages length:', tournament.phases[phaseIndex].matches[matchIndex].result.scorecardImages?.length || 0);
    
    // Save to verify persistence
    await tournament.save();
    
    if (tournament.phases[phaseIndex].matches[matchIndex].result.scorecardImages?.length > 0) {
      console.log('\n✅ SUCCESS: Our fix works! scorecardImages are preserved!');
    } else {
      console.log('\n❌ FAILURE: scorecardImages were still lost!');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

testFix();
