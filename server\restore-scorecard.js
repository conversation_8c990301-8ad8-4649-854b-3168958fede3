const mongoose = require('mongoose');
require('dotenv').config();

async function restoreScorecard() {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/cricket24');
    console.log('Connected to database');
    
    // Find the tournament
    const Tournament = require('./models/Tournament');
    const tournament = await Tournament.findById('681e581dfe3bc0a0414dd937');
    
    if (!tournament) {
      console.log('Tournament not found');
      return;
    }
    
    // Find the specific match
    const matchId = '68787b242a02cd1cf7ccc915';
    let match = null;
    let phaseIndex = -1;
    let matchIndex = -1;
    
    for (let i = 0; i < tournament.phases.length; i++) {
      const foundMatchIndex = tournament.phases[i].matches.findIndex(m => m._id.toString() === matchId);
      if (foundMatchIndex !== -1) {
        match = tournament.phases[i].matches[foundMatchIndex];
        phaseIndex = i;
        matchIndex = foundMatchIndex;
        break;
      }
    }
    
    if (!match) {
      console.log('Match not found');
      return;
    }
    
    console.log('🔍 BEFORE RESTORE:');
    console.log('scorecardImages length:', match.result?.scorecardImages?.length || 0);
    
    // Restore the scorecard image that was originally uploaded
    console.log('\n📸 Restoring original scorecard image...');
    if (!match.result.scorecardImages) {
      match.result.scorecardImages = [];
    }
    
    // Add the original scorecard image that was lost
    match.result.scorecardImages = [{
      url: '/uploads/scorecards/8ab7457f-5e84-4fd4-8851-cb2d360db278-pc_steam_scorecard1.jpg',
      uploadedBy: new mongoose.Types.ObjectId('6817724364f98b5cae6e57f1'),
      uploadedAt: new Date('2025-07-17T04:25:07.714Z'),
      isVerified: false
    }];
    
    console.log('scorecardImages after restore:', match.result.scorecardImages.length);
    console.log('Scorecard URL:', match.result.scorecardImages[0].url);
    
    // Save the tournament to persist the restored image
    await tournament.save();
    console.log('✅ Original scorecard image restored to database');
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

restoreScorecard();
