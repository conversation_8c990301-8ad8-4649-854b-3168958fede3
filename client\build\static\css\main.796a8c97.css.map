{"version": 3, "file": "static/css/main.796a8c97.css", "mappings": "AAAA,KAKE,kCAAmC,CACnC,iCAAkC,CAJlC,mIAEY,CAHZ,QAMF,CAEA,KACE,uEAEF,CCTA,yBACE,YAAa,CACb,cAAe,CACf,QACF,CAGA,6JAUE,wBAA0B,CAN1B,yBAA2B,CAI3B,0BAA4B,CAF5B,0BAA4B,CAG5B,4BAA8B,CAF9B,4BAA8B,CAF9B,oBAMF,CAGA,oCAEE,yBAA2B,CAC3B,4BACF,CAGA,qBACE,yBACF,CAGA,uBAEE,uBAAyB,CADzB,yBAEF,CAEA,qBAGE,2BAA6B,CAF7B,4BAA8B,CAC9B,sBAEF,CAGA,uCAGE,0BAA4B,CAD5B,4BAEF,CCpDA,KACE,iBACF,CAEA,UACE,aAAc,CACd,mBACF,CAEA,8CACE,UACE,2CACF,CACF,CAEA,YAKE,kBAAmB,CAJnB,wBAAyB,CAOzB,UAAY,CALZ,YAAa,CACb,qBAAsB,CAGtB,4BAA6B,CAD7B,sBAAuB,CAJvB,gBAOF,CAEA,UACE,aACF,CAEA,yBACE,GACE,sBACF,CACA,GACE,uBACF,CACF,CClCA,YAQE,kBAAmB,CAPnB,eAAgB,CAYhB,eAAgB,CARhB,YAAa,CACb,qBAAsB,CAFtB,WAAY,CAGZ,sBAAuB,CAIvB,QAAS,CADT,eAAgB,CARhB,gBAAiB,CAOjB,iBAAkB,CANlB,uBAAyB,CASzB,UAEF,CAEA,kBAEE,+BAA0C,CAD1C,0BAEF,CAEA,wBACE,gBAAiB,CACjB,iBAAkB,CAClB,UACF,CAEA,yBACE,cAAe,CACf,eAAgB,CAEhB,aAAc,CADd,YAEF,CAEA,yBACE,eAAiB,CACjB,eAAgB,CAEhB,kBAAmB,CACnB,cAAe,CACf,iBAAkB,CAHlB,wBAIF,CAEA,kBAQE,6BAA8B,CAD9B,uDAA+G,CAJ/G,QAAS,CAFT,UAAW,CAKX,UAAW,CAFX,MAAO,CAFP,iBAAkB,CAGlB,UAIF,CAEA,mBACE,GACE,2BACF,CACA,GACE,0BACF,CACF,CAGA,uBAEE,QAAS,CADT,SAAU,CAEV,UACF,CAEA,iBAQE,kBAAmB,CALnB,YAAa,CACb,qBAAsB,CAFtB,WAAY,CAQZ,0BAA2B,CAH3B,eAAgB,CADhB,eAAgB,CADhB,iBAAkB,CAIlB,iBAAkB,CARlB,uBAUF,CAIA,4BAGE,aAAc,CAFd,gBAAiB,CACjB,iBAAkB,CAElB,uBACF,CAEA,kCACE,qBACF,CAEA,6BACE,gBAAiB,CACjB,eAAgB,CAEhB,eAAgB,CADhB,iBAEF,CAEA,mCAIE,eAA4D,CAA5D,2CAA4D,CAH5D,gBAAkB,CAElB,eAAgB,CADhB,kBAAmB,CAGnB,aACF,CAEA,8BAEE,aAAc,CADd,SAEF,CAGA,uBACE,wBAAyB,CACzB,UACF,CAEA,4BACE,UACF,CAIA,6BACE,uDACF,CAGA,0BAKE,4CACE,SACF,CACF,CAEA,yBACE,yBACE,gBACF,CAEA,wBACE,gBACF,CAEA,YACE,gBACF,CAEA,iBACE,gBACF,CACF,CAEA,yBACE,iBACE,iBACF,CAEA,YACE,gBACF,CAEA,yBACE,gBAAiB,CACjB,YACF,CAEA,yBACE,eAAiB,CACjB,cACF,CAEA,iBACE,gBACF,CAEA,4BACE,gBAAiB,CACjB,iBACF,CAEA,6BACE,cAAe,CACf,iBACF,CAEA,mCACE,eAAiB,CACjB,kBAAmB,CACnB,aACF,CAEA,2BACE,SACF,CAEA,qBACE,gBAAiB,CACjB,iBACF,CACF,CAGA,YACE,kBACF,CAEA,yBAEE,eAAgB,CAChB,eAAgB,CAEhB,iBAAkB,CAJlB,mBAAoB,CAGpB,uBAEF,CAEA,sCAEE,0BAAqC,CADrC,eAEF,CAEA,iDACE,0BACF,CAGA,kBASE,kBAAmB,CAFnB,iBAAkB,CALlB,WAAY,CASZ,0BAAwC,CAHxC,YAAa,CAFb,WAAY,CAIZ,sBAAuB,CATvB,cAAe,CAEf,UAAW,CASX,uBAAyB,CAPzB,UAAW,CADX,YASF,CAEA,wBACE,oBACF,CAGA,uBACE,SAAU,CACV,0BACF,CAEA,8BAGE,oCACF,CAEA,oDALE,SAAU,CACV,uBAOF,CAEA,6BACE,SAAU,CACV,2BAA4B,CAC5B,oCACF,CCjRA,aAEE,wBAAyB,CADzB,iBAAkB,CAYlB,0BAAwC,CADxC,qBAAsB,CANtB,qBAAsB,CAItB,YAAa,CANb,eAAgB,CAKhB,iBAAkB,CADlB,iBAAkB,CAGlB,UAGF,CAEA,+BATE,kBAAmB,CAFnB,YAAa,CAFb,kBAqBF,CARA,kBAGE,iBAAkB,CADlB,WAAY,CAIZ,sBAAuB,CALvB,UAOF,CAEA,mBAIE,UAAc,CAHd,iBAAkB,CAClB,eAAgB,CAChB,iBAEF,CAEA,sBAEE,eAA+B,CAD/B,iBAAmB,CAEnB,kBACF,CAEA,oBAOE,kBAAmB,CAHnB,kBAAmB,CAEnB,YAAa,CADb,eAAgB,CAGhB,sBAAuB,CAPvB,eAAgB,CAEhB,cAAe,CAMf,mBAAoB,CAPpB,UAQF,CAGA,sBACE,WAAY,CACZ,gBAAiB,CACjB,YACF,CAEA,uBAIE,UAAc,CAHd,iBAAkB,CAClB,eAAgB,CAChB,kBAAmB,CAGnB,eAAgB,CADhB,UAEF,CAEA,sBAEE,SAAU,CADV,UAEF,CAEA,sBAEE,kBAAmB,CAEnB,iCAAiD,CAHjD,YAAa,CAEb,cAEF,CAEA,iCACE,kBACF,CAEA,wBAGE,iBAAkB,CADlB,WAAY,CAEZ,iBAAkB,CAHlB,UAIF,CAEA,yBACE,QACF,CAEA,sBAEE,UAAc,CADd,iBAEF,CAEA,yBAEE,WAA+B,CAD/B,gBAEF,CAGA,yBACE,qBAAyB,CACzB,8BACF,CAEA,+BACE,UACF,CAEA,kCACE,WACF,CAEA,mCACE,UACF,CAEA,kCACE,iCACF,CAEA,kCACE,UACF,CAEA,qCACE,WACF,CAGA,2CACE,0BAAyC,CACzC,aACF,CAEA,oCACE,0BAAwC,CACxC,aACF,CAEA,oCACE,0BAAwC,CACxC,aACF,CAEA,0CACE,0BAAyC,CACzC,aACF,CAGA,6CACE,wBAAyB,CACzB,UACF,CAEA,sCACE,wBAAyB,CACzB,UACF,CAEA,sCACE,wBAAyB,CACzB,UACF,CAEA,4CACE,wBAAyB,CACzB,UACF,CCjLA,wBAIE,qDAA4D,CAF5D,aAAc,CADd,gBAAiB,CAEjB,YAEF,CAEA,QAIE,kDAA6D,CAE7D,kBAAmB,CACnB,+BAAyC,CAFzC,UAAY,CAHZ,kBAAmB,CACnB,YAAa,CAFb,iBAOF,CAEA,WAEE,gBAAiB,CACjB,eAAgB,CAFhB,eAGF,CAEA,UAGE,gBAAiB,CAFjB,QAAS,CACT,UAEF,CAEA,SAME,8BAAgC,CAJhC,iBAAkB,CAElB,eAAgB,CADhB,kBAAmB,CAFnB,iBAAkB,CAIlB,iBAEF,CAEA,iBACE,wBAAyB,CAEzB,wBAAyB,CADzB,aAEF,CAEA,eACE,wBAAyB,CAEzB,wBAAyB,CADzB,aAEF,CAEA,qBACE,eAAiB,CAEjB,kBAAmB,CACnB,+BAAyC,CACzC,kBAAmB,CAHnB,YAIF,CAEA,2BAIE,UAAW,CAHX,aAAc,CAId,gBAAiB,CAFjB,eAAgB,CADhB,kBAIF,CAEA,4BAME,qBAAuB,CAHvB,wBAAyB,CACzB,iBAAkB,CAClB,cAAe,CAHf,iBAAkB,CAKlB,gCAAkC,CANlC,UAOF,CAEA,kCAEE,oBAAqB,CACrB,8BAA8C,CAF9C,YAGF,CAEA,SAIE,UAAW,CADX,gBAAiB,CADjB,YAAa,CADb,iBAOF,CAEA,4BALE,eAAiB,CACjB,kBAAmB,CACnB,+BAQF,CALA,mBAEE,YAGF,CAEA,sBAEE,UAAW,CACX,gBAAiB,CACjB,eAAgB,CAHhB,eAIF,CAEA,cAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,yDAEF,CAEA,YAIE,kBAAmB,CAHnB,wBAAyB,CACzB,kBAAmB,CACnB,YAAa,CAGb,iBAAkB,CADlB,uBAEF,CAEA,kBAGE,oBAAqB,CADrB,+BAAyC,CADzC,0BAGF,CAEA,cAME,+BAAgC,CAJhC,6BAA8B,CAE9B,kBAAmB,CACnB,mBAEF,CAEA,qBANE,kBAAmB,CAFnB,YAcF,CANA,OAKE,gBAAiB,CADjB,eAAgB,CADhB,QAGF,CAEA,MACE,UACF,CAEA,IACE,UAAW,CAEX,eAAiB,CADjB,eAEF,CAEA,cAEE,kBAAmB,CACnB,eAAiB,CACjB,eAAgB,CAEhB,mBAAqB,CALrB,gBAAiB,CAIjB,wBAEF,CAEA,kBACE,wBAAyB,CACzB,aACF,CAEA,gBACE,wBAAyB,CACzB,aACF,CAEA,kBACE,wBAAyB,CACzB,aACF,CAEA,eACE,kBACF,CAEA,QAEE,UAAW,CADX,iBAEF,CAEA,eACE,UAAW,CACX,gBACF,CAEA,cACE,eAAiB,CAIjB,6BAA8B,CAF9B,iBAAkB,CAClB,kBAAmB,CAFnB,YAIF,CAEA,QACE,kBACF,CAEA,OAEE,iCAAqC,CADrC,iBAEF,CAEA,QACE,aAAc,CACd,eACF,CAEA,eACE,YAAa,CACb,QAAS,CACT,wBACF,CAEA,KAEE,WAAY,CACZ,iBAAkB,CAClB,cAAe,CAEf,eAAiB,CADjB,eAAgB,CAIhB,mBAAqB,CARrB,gBAAiB,CAOjB,wBAAyB,CADzB,uBAGF,CAEA,UACE,wBAAyB,CACzB,UACF,CAEA,gBACE,wBAAyB,CACzB,0BACF,CAEA,YACE,wBAAyB,CACzB,UACF,CAEA,kBACE,wBAAyB,CACzB,0BACF,CAEA,YACE,wBAAyB,CACzB,UACF,CAEA,kBACE,wBACF,CAEA,UACE,wBAAyB,CACzB,UACF,CAEA,gBACE,wBACF,CAEA,YAIE,eAAiB,CACjB,kBAAmB,CACnB,+BAAyC,CAHzC,UAAW,CADX,YAAa,CADb,iBAMF,CAEA,eASE,kBAAmB,CAEnB,6BAA+B,CAL/B,0BAAoC,CADpC,QAAS,CAET,YAAa,CACb,sBAAuB,CALvB,MAAO,CAFP,cAAe,CAGf,OAAQ,CAFR,KAAM,CAQN,YAEF,CAEA,OAQE,8BAAgC,CAPhC,eAAiB,CACjB,kBAAmB,CAKnB,gCAA0C,CAF1C,eAAgB,CADhB,eAAgB,CAEhB,eAAgB,CAHhB,SAMF,CAEA,cAGE,kBAAmB,CAGnB,kBAAmB,CADnB,+BAAgC,CAEhC,2BAA4B,CAN5B,YAAa,CACb,6BAA8B,CAE9B,iBAIF,CAEA,iBAEE,UAAW,CACX,gBAAiB,CAFjB,QAGF,CAEA,WAUE,kBAAmB,CATnB,eAAgB,CAChB,WAAY,CAUZ,iBAAkB,CAPlB,UAAW,CADX,cAAe,CAKf,YAAa,CANb,gBAAiB,CAKjB,WAAY,CAGZ,sBAAuB,CALvB,SAAU,CAOV,uBAAyB,CANzB,UAOF,CAEA,iBACE,wBAAyB,CACzB,UACF,CAEA,YACE,YACF,CAEA,YACE,kBACF,CAEA,kBAIE,UAAW,CAHX,aAIF,CAEA,qCAIE,wBAAyB,CACzB,iBAAkB,CAGlB,qBAAsB,CAFtB,cAAe,CAHf,iBAAkB,CAIlB,gCAAkC,CALlC,UAOF,CAEA,iDAGE,oBAAqB,CACrB,8BAA8C,CAF9C,YAGF,CAEA,gBAGE,4BAA6B,CAF7B,eAAgB,CAChB,gBAEF,CAEA,mBAEE,UAAW,CACX,gBAAiB,CAFjB,eAGF,CAEA,YAGE,kBAAmB,CACnB,iBAAkB,CAHlB,kBAAmB,CACnB,YAGF,CAEA,eAEE,UAAW,CACX,cAAe,CAFf,eAGF,CAEA,cAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,iCAEF,CAEA,oBACE,QACF,CAEA,cAME,kBAAmB,CACnB,2BAA4B,CAF5B,4BAA6B,CAJ7B,YAAa,CAEb,QAAS,CADT,wBAAyB,CAEzB,iBAIF,CAEA,mBACE,GACE,SAAU,CACV,2BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,kBACE,GACE,SACF,CACA,GACE,SACF,CACF,CAEA,mBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,yBACE,wBACE,YACF,CAEA,cACE,yBACF,CAEA,eACE,sBACF,CAEA,OACE,qBAAsB,CACtB,OAAQ,CACR,iBACF,CAEA,cACE,yBACF,CAEA,OAEE,WAAY,CADZ,SAEF,CAEA,cACE,qBACF,CAEA,mBACE,UACF,CACF,CCpeA,qBAKE,kDAA6D,CAC7D,uBAAkC,CAElC,2BAA4B,CAD5B,yBAA0B,CAO1B,kBAAmB,CACnB,gCAA0C,CAF1C,cAAe,CAGf,2CAA+C,CAb/C,YAAa,CASb,aAAc,CAHd,gBAAiB,CARjB,iBAAkB,CAUlB,sBAAyB,CATzB,WAAY,CAQZ,SAOF,CAGA,4BAOE,kDAA6D,CAC7D,kBAAmB,CAPnB,UAAW,CAKX,WAAY,CAFZ,MAAO,CAFP,iBAAkB,CAClB,KAAM,CAEN,UAAW,CAIX,UACF,CAGA,2BAOE,6DAKC,CAXD,UAAW,CAKX,WAAY,CAFZ,SAAU,CAWV,mBAAoB,CAbpB,iBAAkB,CAClB,KAAM,CAUN,uBAAwB,CARxB,UAAW,CASX,SAEF,CAEA,2BAEE,4BAA0C,CAD1C,0BAEF,CAGA,uCAUE,kBAAmB,CALnB,sBAAoC,CAQpC,0BAA0C,CAP1C,iBAAkB,CAQlB,8BAAwC,CAFxC,cAAe,CAHf,YAAa,CADb,WAAY,CAGZ,sBAAuB,CARvB,SAAU,CAFV,iBAAkB,CAClB,QAAS,CAaT,uBAAyB,CARzB,UAAW,CAHX,UAYF,CAEA,6CACE,sBACF,CAGA,sCAGE,aAAc,CADd,YAAa,CAEb,gBAAiB,CAHjB,iBAIF,CAEA,0DAGE,eAAgB,CADhB,kBAAmB,CAEnB,gBAAiB,CAHjB,iBAAkB,CAIlB,wBACF,CAEA,yEACE,gBAAiB,CACjB,eAAgB,CAChB,wBACF,CAEA,2EACE,gBAAiB,CACjB,gBACF,CAEA,yEASE,sBAAoC,CADpC,sBAA0C,CAH1C,iBAAkB,CAElB,8BAAwC,CANxC,aAAc,CAEd,WAAY,CACZ,cAAgB,CAEhB,eAAgB,CAJhB,YAQF,CAEA,6EAEE,WAAY,CACZ,kBAAmB,CACnB,WAAY,CAHZ,UAIF,CAEA,uEAKE,iBAAkB,CAElB,8BAAwC,CANxC,aAAc,CAEd,WAAY,CACZ,gBAAkB,CAElB,eAAgB,CAJhB,YAMF,CAEA,2EAEE,WAAY,CACZ,kBAAmB,CAFnB,UAGF,CAGA,sDAEE,YAAa,CACb,aAAc,CACd,eAAgB,CAChB,iBAAkB,CAJlB,WAKF,CAEA,0DAME,QAAS,CAJT,WAAY,CACZ,kBAAmB,CACnB,iBAAkB,CAClB,aAAc,CAEd,6BAA+B,CAN/B,UAOF,CAEA,gEACE,qBACF,CAEA,oEAYE,eAAgB,CAThB,aAAe,CAEf,cAAe,CACf,eAAgB,CAGhB,WAAY,CALZ,eAAgB,CAMhB,gBAAiB,CATjB,iBAAkB,CAClB,OAAQ,CASR,gBAAiB,CAJjB,wBAAyB,CACzB,UAKF,CAEA,yEAGE,0BAAoC,CAEpC,iBAAkB,CAJlB,iBAAmB,CAGnB,eAAgB,CAFhB,wBAIF,CAGA,yCACE,iBACF,CAEA,sDAGE,aAAc,CAFd,aAAc,CAKd,WAAY,CADZ,aAAc,CAHd,eAAiB,CAKjB,iBAAkB,CAHlB,SAAU,CAIV,SACF,CAEA,mEAME,iCAAiD,CAJjD,aAAc,CAEd,gBAAiB,CAKjB,kBAAmB,CADnB,eAAgB,CADhB,oBAAsB,CAJtB,iBAAkB,CAElB,wBAAyB,CAJzB,UASF,CAEA,wEACE,aAAc,CAEd,eAAgB,CADhB,wBAEF,CAEA,uEAEE,YAAa,CACb,sBAAuB,CAFvB,iBAGF,CAEA,4FACE,gCAAgD,CAChD,gBACF,CAEA,iGACE,YAAa,CACb,gBAAiB,CAEjB,mBAAqB,CADrB,wBAEF,CAEA,uHAEE,eAAgB,CADhB,kBAAoB,CAEpB,wBACF,CAEA,uHACE,eAAgB,CAChB,UACF,CAEA,uGACE,QACF,CAGA,qCAKE,YAAa,CACb,OAAQ,CALR,iBAAkB,CAElB,UAAW,CADX,QAAS,CAET,UAGF,CAEA,sGAGE,sBAAoC,CAIpC,sBAA0C,CAL1C,UAAY,CAGZ,WAAY,CACZ,uBAAyB,CAFzB,UAIF,CAEA,wDACE,wBAAyB,CACzB,0BACF,CAEA,0DACE,wBAAyB,CACzB,0BACF,CAGA,mCACE,qBACE,gCACF,CAEA,yEACE,0BACF,CAEA,wEACE,wBACF,CAEA,uHACE,wBACF,CACF,CAGA,yBACE,qBAEE,YAAa,CADb,WAEF,CAEA,yEACE,cACF,CAEA,sDAEE,YAAa,CADb,WAEF,CAEA,4FACE,cACF,CACF,CAEA,yBACE,qBAEE,YAAa,CADb,WAEF,CAEA,yEACE,gBACF,CAEA,sDAEE,YAAa,CADb,WAEF,CAEA,mEACE,gBACF,CAEA,4FACE,gBACF,CAEA,iGACE,cACF,CACF,CAGA,yBACE,qBAEE,YAAa,CADb,WAEF,CAEA,yEACE,gBACF,CAEA,2EACE,gBACF,CAEA,sDAEE,YAAa,CADb,WAEF,CAEA,4FACE,gBACF,CAEA,iGACE,eACF,CACF,CC3XA,MACE,uBAAwB,CACxB,sBAA0B,CAC1B,wBAAyB,CACzB,yBAA0B,CAC1B,2BAA4B,CAC5B,yBAA0B,CAC1B,gCAAwC,CACxC,sCACF,CAGA,YACE,uBAAwB,CACxB,yBAA0B,CAC1B,wBAAyB,CACzB,yBAA0B,CAC1B,2BAA4B,CAC5B,yBAA0B,CAC1B,gCAAuC,CACvC,kCACF,CAEA,kBAKE,kBAAgC,CAAhC,+BAAgC,CAChC,aAA6B,CAA7B,4BAA6B,CAH7B,aAAc,CADd,gBAAiB,CAEjB,gBAAiB,CAHjB,YAAa,CAMb,mDACF,CAEA,yBAGE,eAAkC,CAAlC,iCAAkC,CAIlC,wBAAwC,CAAxC,uCAAwC,CAFxC,kBAAmB,CACnB,+BAA4B,CAA5B,2BAA4B,CAJ5B,kBAAmB,CAEnB,YAAa,CAHb,iBAAkB,CAOlB,8EACF,CAEA,4BACE,aAA6B,CAA7B,4BAA6B,CAE7B,cAAe,CACf,eAAgB,CAFhB,kBAAmB,CAGnB,yBACF,CAEA,2BACE,aAA+B,CAA/B,8BAA+B,CAC/B,cAAe,CACf,kBAAmB,CACnB,yBACF,CAEA,cAIE,kBAAiC,CAAjC,gCAAiC,CAGjC,6BAA8B,CAD9B,iBAAkB,CAHlB,aAAc,CADd,eAAgB,CAGhB,YAAa,CAGb,oCACF,CAEA,iBACE,aAA6B,CAA7B,4BAA6B,CAC7B,kBAAmB,CACnB,yBACF,CAEA,iBACE,eAEF,CAEA,iBAEE,aAA+B,CAA/B,8BAA+B,CAD/B,iBAAkB,CAElB,yBACF,CAEA,eAOE,aAAS,CANT,eAAkC,CAAlC,iCAAkC,CAQlC,wBAAwC,CAAxC,uCAAwC,CANxC,kBAAmB,CAKnB,+BAA4B,CAA5B,2BAA4B,CAH5B,YAAa,CAEb,QAAS,CADT,iCAAkC,CAFlC,kBAAmB,CAFnB,YAAa,CAQb,8EACF,CAEA,YACE,YAAa,CACb,qBACF,CAEA,kBAGE,aAA6B,CAA7B,4BAA6B,CAF7B,eAAgB,CAChB,iBAAkB,CAElB,yBACF,CAEA,uCAME,eAAkC,CAAlC,iCAAkC,CAHlC,wBAAwC,CAAxC,uCAAwC,CACxC,iBAAkB,CAGlB,aAA6B,CAA7B,4BAA6B,CAF7B,cAAe,CAHf,iBAAkB,CAMlB,oEACF,CAEA,mDAGE,oBAAqB,CACrB,8BAA4C,CAF5C,YAGF,CAGA,+DAEE,kBAAiC,CAAjC,gCACF,CAEA,yFAEE,aAA+B,CAA/B,8BACF,CAEA,qBAEE,eAAgB,CADhB,eAEF,CAEA,0BAGE,aAAS,CACT,iBAAkB,CAHlB,YAAa,CAEb,QAAS,CADT,+BAAgC,CAGhC,gBACF,CAEA,kBASE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAEtB,sBAAuB,CAJvB,gBAMF,CAEA,kCAbE,eAAkC,CAAlC,iCAAkC,CAIlC,wBAAwC,CAAxC,uCAAwC,CAHxC,kBAAmB,CAEnB,+BAA4B,CAA5B,2BAA4B,CAD5B,YAAa,CAQb,8EAWF,CARA,gBAKE,kBAGF,CAEA,gBAEE,aAA6B,CAA7B,4BAA6B,CAC7B,cAAe,CAFf,kBAAmB,CAGnB,yBACF,CAEA,iBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,yBAA0B,CAE1B,kBACF,CAEA,gBAEE,kBAAmB,CAGnB,eAAkC,CAAlC,iCAAkC,CADlC,wBAAwC,CAAxC,uCAAwC,CAExC,iBAAkB,CAMlB,aAA6B,CAA7B,4BAA6B,CAL7B,cAAe,CANf,YAAa,CAQb,cAAe,CAEf,eAAgB,CARhB,iBAAkB,CAOlB,eAAgB,CAFhB,kBAKF,CAEA,sBACE,kBAAiC,CAAjC,gCAAiC,CACjC,0BACF,CAEA,uBACE,kBAAmB,CACnB,oBAAqB,CACrB,UAAY,CACZ,eACF,CAEA,iBAGE,iBAAkB,CAElB,aAAc,CAHd,WAAY,CAEZ,gBAAiB,CAHjB,UAKF,CAEA,iBAEE,aAA6B,CAA7B,4BAA6B,CAC7B,cAAe,CAFf,kBAAmB,CAGnB,yBACF,CAEA,mBAME,eAAkC,CAAlC,iCAAkC,CAHlC,wBAAwC,CAAxC,uCAAwC,CACxC,iBAAkB,CAClB,kBAAmB,CAJnB,gBAAiB,CACjB,eAAgB,CAKhB,0DACF,CAEA,aAEE,kBAAmB,CAGnB,+BAA+C,CAA/C,8CAA+C,CAJ/C,YAAa,CAEb,6BAA8B,CAC9B,iBAAkB,CAElB,yBACF,CAEA,mBACE,kBAAiC,CAAjC,gCACF,CAEA,wBACE,kBACF,CAEA,aAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAEA,kBAGE,aAA6B,CAA7B,4BAA6B,CAF7B,eAAgB,CAChB,gBAAiB,CAEjB,yBACF,CAEA,mBACE,aAA+B,CAA/B,8BAA+B,CAC/B,cAAe,CACf,yBACF,CAEA,YACE,kBAAmB,CAEnB,WAAY,CACZ,iBAAkB,CAFlB,UAAY,CAKZ,cAAe,CACf,cAAe,CAFf,WAAY,CAGZ,aAAc,CACd,yBAA2B,CAL3B,UAMF,CAEA,kBACE,kBACF,CAEA,iBAGE,kBAAiC,CAAjC,gCAAiC,CAEjC,wBAAwC,CAAxC,uCAAwC,CADxC,iBAAkB,CAHlB,kBAAmB,CACnB,YAAa,CAIb,0DACF,CAEA,mBACE,YAAa,CAEb,cAAe,CADf,QAEF,CAEA,kBAEE,iBAAkB,CAClB,cAAe,CACf,eAAgB,CAHhB,gBAAiB,CAIjB,kBACF,CAEA,wBACE,kBAAmB,CAEnB,wBAAyB,CADzB,aAEF,CAEA,0BACE,kBAAmB,CAEnB,wBAAyB,CADzB,aAEF,CAEA,kBACE,YAAa,CACb,QACF,CAEA,4CAME,WAAY,CACZ,iBAAkB,CAElB,cAAe,CALf,QAAO,CAOP,cAAe,CAHf,eAAgB,CAHhB,iBAAkB,CAKlB,kBAEF,CAEA,UACE,kBAAmB,CACnB,UACF,CAEA,+BACE,kBAAmB,CACnB,0BACF,CAEA,mBACE,kBAAmB,CACnB,kBAAmB,CACnB,cACF,CAEA,WACE,kBAAmB,CACnB,UACF,CAEA,iBACE,kBAAmB,CACnB,0BACF,CAEA,YACE,kBAAmB,CACnB,UACF,CAEA,kBACE,kBAAmB,CACnB,0BACF,CAEA,WACE,kBAAmB,CACnB,UACF,CAEA,iBACE,kBAAmB,CACnB,0BACF,CAGA,eAGE,kBAAiC,CAAjC,gCAAiC,CAEjC,wBAAwC,CAAxC,uCAAwC,CADxC,iBAAkB,CAHlB,eAAgB,CAChB,YAAa,CAIb,0DACF,CAEA,kBAEE,aAA6B,CAA7B,4BAA6B,CAC7B,cAAe,CAFf,eAAkB,CAGlB,yBACF,CAEA,gBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,yBAA0B,CAE1B,gBAAiB,CACjB,eACF,CAEA,eAGE,kBAAmB,CAEnB,eAAkC,CAAlC,iCAAkC,CAElC,wBAAwC,CAAxC,uCAAwC,CADxC,iBAAkB,CALlB,YAAa,CACb,6BAA8B,CAE9B,YAAa,CAIb,kBACF,CAEA,qBACE,oBAAqB,CACrB,+BAAkC,CAAlC,iCACF,CAEA,eACE,QACF,CAEA,kBAEE,aAA6B,CAA7B,4BAA6B,CAC7B,cAAe,CAFf,cAAiB,CAGjB,yBACF,CAEA,iBAGE,cAAe,CAFf,cAIF,CAEA,sCALE,aAA+B,CAA/B,8BAA+B,CAE/B,yBAOF,CAJA,qBAEE,cAEF,CAEA,wBACE,YAAa,CACb,OACF,CAEA,sCAIE,WAAY,CACZ,iBAAkB,CAElB,cAAe,CADf,cAAe,CAHf,gBAAiB,CAKjB,kBACF,CAEA,UACE,kBAAmB,CACnB,UACF,CAEA,gBACE,kBACF,CAEA,UACE,kBAAmB,CACnB,UACF,CAEA,gBACE,kBACF,CAEA,kBACE,kBAAmB,CACnB,UACF,CAEA,wBACE,kBACF,CAGA,0BACE,0BAEE,QAAS,CADT,+BAEF,CACF,CAEA,0BACE,kBACE,YACF,CAEA,0BAEE,QAAS,CADT,yBAEF,CAEA,gBACE,QAAS,CACT,YACF,CAEA,iBAEE,QAAS,CADT,wDAEF,CACF,CAEA,yBACE,kBACE,YACF,CAEA,eAEE,QAAS,CACT,YACF,CAEA,gCALE,yBAOF,CAEA,kBACE,qBAAsB,CACtB,QACF,CAMA,kCACE,YACF,CACF,CAGA,OAOE,eAAkC,CAAlC,iCAAkC,CAFlC,wBAAwC,CAAxC,uCAAwC,CACxC,iBAAkB,CAHlB,aAAc,CADd,WAAY,CADZ,cAAe,CAGf,0BAIF,CAEA,aACE,oBACF,CAGA,sCACE,SACF,CAEA,4CACE,kBAAiC,CAAjC,gCAAiC,CACjC,iBACF,CAEA,4CACE,kBAAkC,CAAlC,iCAAkC,CAClC,iBACF,CAEA,kDACE,kBAAoC,CAApC,mCACF,CAGA,wDACE,kBAAiC,CAAjC,gCACF,CAEA,wDACE,kBACF,CAEA,8DACE,kBACF,CAGA,cACE,kBAAmB,CACnB,wBAAyB,CACzB,iBAAkB,CAElB,aAAc,CADd,YAAa,CAEb,eACF,CAOA,kCAHE,aAAc,CADd,eAQF,CAJA,iBAEE,iBAEF,CAEA,iBACE,iBACF,CAEA,gBAEE,aAAc,CACd,iBAAkB,CAFlB,eAGF,CAGA,iBAGE,kBAAmB,CAEnB,kBAAiC,CAAjC,gCAAiC,CAGjC,wBAAwC,CAAxC,uCAAwC,CAFxC,iBAAkB,CALlB,YAAa,CACb,6BAA8B,CAK9B,kBAAmB,CAHnB,YAAa,CAKb,0DACF,CAEA,6BAGE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAEA,yCAGE,aAA6B,CAA7B,4BAA6B,CAD7B,eAAgB,CAEhB,gBAAiB,CACjB,yBACF,CAEA,4BAEE,YAAa,CACb,OACF,CAEA,+BAIE,eAAkC,CAAlC,iCAAkC,CADlC,wBAAwC,CAAxC,uCAAwC,CAExC,iBAAkB,CAIlB,aAA6B,CAA7B,4BAA6B,CAH7B,cAAe,CAEf,cAAe,CANf,gBAAiB,CAKjB,kBAGF,CAEA,2CAEE,kBAAiC,CAAjC,gCAAiC,CACjC,oBAAoC,CAApC,mCACF,CAEA,iBACE,kBAAmB,CAEnB,oBAAqB,CADrB,UAEF,CAEA,mBAEE,kBAAiC,CAAjC,gCAAiC,CACjC,iBAAkB,CAIlB,aAA6B,CAA7B,4BAA6B,CAH7B,eAAgB,CAChB,cAAe,CAJf,gBAAiB,CAKjB,iBAAkB,CAGlB,yEACF,CAEA,mCAJE,wBAAwC,CAAxC,uCAUF,CANA,gBAIE,iBAAkB,CAFlB,gBAAiB,CADjB,aAAc,CAId,gCACF,CAEA,cAGE,kBAAiC,CAAjC,gCAAiC,CACjC,wBAAwC,CAAxC,uCAAwC,CACxC,iBAAkB,CAClB,cAAe,CALf,eAAgB,CAChB,YAAa,CAKb,0DACF,CAEA,gBAEE,aAA+B,CAA/B,8BAA+B,CAD/B,YAAa,CAEb,yBACF,CAEA,sBAGE,kBAAiC,CAAjC,gCAAiC,CACjC,wBAAwC,CAAxC,uCAAwC,CACxC,iBAAkB,CAJlB,eAAgB,CAChB,YAAa,CAIb,0DACF,CAEA,yBAEE,aAA6B,CAA7B,4BAA6B,CAC7B,cAAe,CAFf,cAAiB,CAGjB,yBACF,CAEA,wBAEE,aAA+B,CAA/B,8BAA+B,CAC/B,cAAe,CAFf,YAAa,CAGb,yBACF,CCpvBA,kCAME,kDAA6D,CAC7D,uBAAkC,CAElC,2BAA4B,CAD5B,yBAA0B,CAQ1B,kBAAmB,CACnB,gCAA0C,CAF1C,cAAe,CAGf,2CAA+C,CAd/C,YAAa,CAUb,kBAAmB,CAXnB,eAAgB,CAOhB,gBAAiB,CATjB,iBAAkB,CAWlB,sBAAyB,CAVzB,UAAW,CASX,SAQF,CAGA,yCAOE,kDAA6D,CAC7D,kBAAmB,CAPnB,UAAW,CAKX,WAAY,CAFZ,MAAO,CAFP,iBAAkB,CAClB,KAAM,CAEN,UAAW,CAIX,UACF,CAGA,wCAOE,6DAKC,CAXD,UAAW,CAKX,WAAY,CAFZ,SAAU,CAWV,mBAAoB,CAbpB,iBAAkB,CAClB,KAAM,CAUN,uBAAwB,CARxB,UAAW,CASX,SAEF,CAEA,wCAEE,4BAA0C,CAD1C,0BAEF,CAGA,mDAKE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAHtB,UAAW,CAMX,iBAAkB,CALlB,UAAW,CAIX,SAEF,CAGA,uEAIE,sBAAuB,CAFvB,YAAa,CACb,qBAAsB,CAQtB,SAAU,CALV,mBAAqB,CADrB,SAAU,CAEV,iBAAkB,CAElB,iBAAkB,CAClB,QAAS,CATT,UAAW,CAOX,SAIF,CAIA,sFAME,kBAAmB,CADnB,YAAa,CAFb,cAAe,CACf,eAAgB,CAFhB,aAAc,CAKd,0BAA2B,CAC3B,mBAAqB,CACrB,cAAe,CACf,iBAAkB,CAElB,6BAA6C,CAX7C,YAAa,CAUb,SAEF,CAEA,wFAKE,UAAW,CAFX,gBAAiB,CACjB,eAAgB,CAQhB,mBAAqB,CAHrB,iBAAkB,CAElB,+CAA6E,CAD7E,SAIF,CAEA,8KATE,kBAAmB,CADnB,YAAa,CAJb,aAAc,CAMd,0BAA2B,CAK3B,cAAe,CAZf,YAsBF,CAEA,0FAGE,iBAAkB,CAElB,4BAA4C,CAH5C,aAAc,CAEd,gBAAiB,CAHjB,YAAa,CAKb,SACF,CAGA,mEAQE,kBAAmB,CAFnB,YAAa,CAJb,YAAa,CAKb,sBAAuB,CAHvB,kBAAmB,CACnB,eAAgB,CAFhB,iBAAkB,CAFlB,WAAY,CAQZ,SACF,CAEA,uEAKE,kBAAmB,CACnB,2BAAyC,CAJzC,YAAa,CACb,kBAAmB,CACnB,6BAA8B,CAH9B,WAMF,CAGA,sDAEE,YAAa,CACb,sBAAuB,CACvB,iBAAkB,CAGlB,eAAgB,CADhB,iBAAkB,CALlB,UAAW,CAIX,SAGF,CAEA,2DAGE,UAAW,CAIX,aAAc,CANd,gBAAiB,CACjB,eAAgB,CAGhB,mBAAqB,CAKrB,aAAc,CADd,eAAgB,CAEhB,iBAAkB,CAHlB,sBAAuB,CAJvB,iCAA2C,CAE3C,kBAMF,CAGA,sDAKE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAHtB,UAAW,CAOX,aAAc,CADd,iBAAkB,CALlB,UAAW,CAIX,SAGF,CAGA,mEAKE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAFtB,WAAY,CAIZ,eAAiB,CALjB,UAMF,CAGA,gFAGE,mBAAqB,CADrB,iBAAkB,CADlB,UAGF,CAGA,oFAKE,0BAAoC,CACpC,iBAAkB,CAHlB,mBAAqB,CACrB,kBAAoB,CAFpB,iBAAkB,CADlB,UAMF,CAEA,qFAGE,UAAW,CAFX,gBAAiB,CACjB,eAAgB,CAEhB,wBACF,CAGA,qFAEE,YAAa,CACb,qBAAsB,CACtB,cAAgB,CAChB,cAAe,CAJf,UAKF,CAEA,+FACE,YAAa,CACb,6BAA8B,CAC9B,mBACF,CAEA,gGAEE,kBAAmB,CADnB,YAAa,CAEb,SACF,CAEA,iGAGE,UAAW,CAFX,eAAiB,CACjB,eAAgB,CAEhB,kBACF,CAEA,iGAEE,UAAW,CADX,eAEF,CAGA,kDACE,iBAAkB,CAElB,UAAW,CADX,QAAS,CAET,UACF,CAEA,sDAGE,SAAU,CAFV,iBAAkB,CAClB,QAAS,CAET,UACF,CAEA,kDAKE,YAAa,CACb,mBAAoB,CALpB,iBAAkB,CAElB,UAAW,CADX,QAAS,CAET,WAGF,CAEA,gDAIE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAItB,mBAAqB,CAFrB,gBAAkB,CAClB,cAAe,CALf,UAOF,CAEA,6DAIE,kBAAmB,CAEnB,sBAAoC,CAEpC,iBAAkB,CANlB,YAAa,CACb,qBAAsB,CAEtB,mBAAqB,CAErB,aAAe,CANf,UAQF,CAEA,wEAEE,UAAW,CADX,eAAiB,CAEjB,eACF,CAEA,yEAGE,UAAW,CAFX,gBAAiB,CACjB,eAEF,CAEA,0EAEE,UAAW,CADX,eAAiB,CAEjB,UACF,CAEA,2DAEE,kBAAmB,CAMnB,sBAAoC,CAEpC,iBAAkB,CAElB,qBAAsB,CAPtB,UAAW,CAJX,YAAa,CAKb,eAAiB,CAFjB,SAAW,CADX,sBAAuB,CAIvB,gBAAkB,CAMlB,eAAgB,CAJhB,mBAAsB,CAKtB,sBAAuB,CACvB,kBAAmB,CAJnB,UAKF,CAEA,mDAME,kBAAmB,CADnB,eAAgB,CAEhB,kBAAgB,CANhB,iBAAkB,CAClB,UAAW,CAEX,UAIF,CAEA,0DACE,kDAA6D,CAE7D,eAAgB,CADhB,uBAEF,CAEA,+EAEE,8BAAwC,CADxC,0BAEF,CAEA,mEACE,kDAA6D,CAC7D,UACF,CAGA,yBACE,kCAGE,YAAa,CACb,kBAAmB,CAFnB,eAAgB,CADhB,UAIF,CAEA,uEACE,SACF,CAEA,sFACE,gBACF,CAEA,mEAEE,YAAa,CACb,kBAAmB,CAFnB,WAGF,CAEA,uEAEE,YAAa,CACb,kBAAmB,CAFnB,WAGF,CAGA,2DACE,gBAAiB,CACjB,mBACF,CAEA,kMAEE,gBACF,CACF,CAEA,yBACE,kCAGE,YAAa,CACb,kBAAmB,CAFnB,eAAgB,CADhB,UAIF,CAEA,uEACE,SACF,CAEA,sFACE,gBACF,CAEA,mEAEE,YAAa,CACb,kBAAmB,CAFnB,WAGF,CAEA,uEAEE,YAAa,CACb,kBAAmB,CAFnB,WAGF,CAGA,sDACE,iBACF,CAEA,2DACE,gBAAiB,CACjB,gBAAmB,CACnB,aACF,CAEA,qFACE,gBACF,CAEA,qFACE,cACF,CAEA,+FACE,mBACF,CAEA,kMAEE,eACF,CAEA,yEACE,gBACF,CACF", "sources": ["index.css", "styles/playerForm.css", "App.css", "styles/adminDashboard.css", "styles/mobileDashboard.css", "components/AdminMatchManagement.css", "components/CricketPlayerCard.css", "components/admin/TemplateBuilder.css", "components/auction/AuctionPlayerCard.css"], "sourcesContent": ["body {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n", "/* Player Form Styles */\n\n/* Fix for the Player Type and Batting Hand buttons */\n.playing-style-container {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 10px;\n}\n\n/* Make the buttons wider and ensure text is fully visible */\n.playing-style-container button,\n.playing-style-container .MuiButtonBase-root,\n.playing-style-container .MuiButton-root,\n.playing-style-container [role=\"button\"] {\n  min-width: 120px !important;\n  width: auto !important;\n  padding: 8px 16px !important;\n  white-space: nowrap !important;\n  overflow: visible !important;\n  text-overflow: clip !important;\n  font-size: 14px !important;\n}\n\n/* Ensure dropdown menus are wide enough */\n.MuiSelect-select,\n.MuiMenuItem-root {\n  min-width: 150px !important;\n  white-space: nowrap !important;\n}\n\n/* Fix for the bowling hand dropdown */\n.bowling-hand-select {\n  min-width: 180px !important;\n}\n\n/* Improve overall form layout */\n.player-form-container {\n  max-width: 800px !important;\n  margin: 0 auto !important;\n}\n\n.player-form-section {\n  margin-bottom: 20px !important;\n  padding: 15px !important;\n  border-radius: 8px !important;\n}\n\n/* Make sure labels are fully visible */\n.MuiFormLabel-root,\n.MuiInputLabel-root {\n  white-space: nowrap !important;\n  overflow: visible !important;\n}\n", ".App {\n  text-align: center;\n}\n\n.App-logo {\n  height: 40vmin;\n  pointer-events: none;\n}\n\n@media (prefers-reduced-motion: no-preference) {\n  .App-logo {\n    animation: App-logo-spin infinite 20s linear;\n  }\n}\n\n.App-header {\n  background-color: #282c34;\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  font-size: calc(10px + 2vmin);\n  color: white;\n}\n\n.App-link {\n  color: #61dafb;\n}\n\n@keyframes App-logo-spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n", "/* Admin Dashboard Styles */\n\n/* Stats Cards */\n.stats-card {\n  border-radius: 0;\n  padding: 16px 8px;\n  transition: all 0.3s ease;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  position: relative;\n  overflow: hidden;\n  margin: 0;\n  width: 100%;\n  box-shadow: none;\n}\n\n.stats-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.15);\n}\n\n.stats-card .stats-icon {\n  font-size: 1.5rem;\n  margin-bottom: 4px;\n  opacity: 0.9;\n}\n\n.stats-card .stats-value {\n  font-size: 2rem;\n  font-weight: 700;\n  margin: 4px 0;\n  line-height: 1;\n}\n\n.stats-card .stats-label {\n  font-size: 0.8rem;\n  font-weight: 500;\n  text-transform: uppercase;\n  letter-spacing: 1px;\n  margin-top: 2px;\n  text-align: center;\n}\n\n.stats-card::after {\n  content: '';\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  width: 100%;\n  height: 4px;\n  background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.8) 50%, rgba(255,255,255,0) 100%);\n  animation: shimmer 2s infinite;\n}\n\n@keyframes shimmer {\n  0% {\n    transform: translateX(-100%);\n  }\n  100% {\n    transform: translateX(100%);\n  }\n}\n\n/* Admin Tools */\n.admin-tools-container {\n  padding: 0;\n  margin: 0;\n  width: 100%;\n}\n\n.admin-tool-card {\n  transition: all 0.3s ease;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  position: relative;\n  overflow: hidden;\n  margin-bottom: 0;\n  align-items: center;\n  text-align: center;\n  justify-content: flex-start;\n}\n\n/* Hover effects are now handled by inline styles */\n\n.admin-tool-card .tool-icon {\n  font-size: 1.5rem;\n  margin-bottom: 8px;\n  color: #1976d2;\n  transition: all 0.3s ease;\n}\n\n.admin-tool-card:hover .tool-icon {\n  transform: scale(1.05);\n}\n\n.admin-tool-card .tool-title {\n  font-size: 1.1rem;\n  font-weight: 600;\n  margin-bottom: 8px;\n  line-height: 1.2;\n}\n\n.admin-tool-card .tool-description {\n  font-size: 0.85rem;\n  margin-bottom: 12px;\n  line-height: 1.4;\n  color: var(--text-secondary-color, rgba(255, 255, 255, 0.7));\n  max-width: 90%;\n}\n\n.admin-tool-card .tool-action {\n  width: 80%;\n  margin: 0 auto;\n}\n\n/* Dark mode specific styles */\n.dark-mode .stats-card {\n  background-color: #2d2d2d;\n  color: #ffffff;\n}\n\n.dark-mode .admin-tool-card {\n  color: #ffffff;\n}\n\n/* Dark mode hover effects are now handled by inline styles */\n\n.dark-mode .stats-card::after {\n  background: linear-gradient(90deg, rgba(0,0,0,0) 0%, rgba(255,255,255,0.2) 50%, rgba(0,0,0,0) 100%);\n}\n\n/* Responsive styles */\n@media (max-width: 1200px) {\n  .stats-container {\n    padding: 0;\n  }\n\n  .admin-dashboard-container {\n    padding: 0;\n  }\n}\n\n@media (max-width: 960px) {\n  .stats-card .stats-value {\n    font-size: 1.7rem;\n  }\n\n  .stats-card .stats-icon {\n    font-size: 1.5rem;\n  }\n\n  .stats-card {\n    padding: 12px 4px;\n  }\n\n  .admin-tool-card {\n    padding: 12px 8px;\n  }\n}\n\n@media (max-width: 600px) {\n  .stats-container {\n    margin-bottom: 8px;\n  }\n\n  .stats-card {\n    padding: 10px 4px;\n  }\n\n  .stats-card .stats-value {\n    font-size: 1.5rem;\n    margin: 2px 0;\n  }\n\n  .stats-card .stats-label {\n    font-size: 0.7rem;\n    margin-top: 2px;\n  }\n\n  .admin-tool-card {\n    padding: 12px 8px;\n  }\n\n  .admin-tool-card .tool-icon {\n    font-size: 1.5rem;\n    margin-bottom: 6px;\n  }\n\n  .admin-tool-card .tool-title {\n    font-size: 1rem;\n    margin-bottom: 6px;\n  }\n\n  .admin-tool-card .tool-description {\n    font-size: 0.8rem;\n    margin-bottom: 10px;\n    max-width: 95%;\n  }\n\n  .admin-dashboard-container {\n    padding: 0;\n  }\n\n  .admin-section-title {\n    font-size: 1.3rem;\n    margin-bottom: 8px;\n  }\n}\n\n/* Tab styles */\n.admin-tabs {\n  margin-bottom: 16px;\n}\n\n.admin-tabs .MuiTab-root {\n  text-transform: none;\n  font-weight: 500;\n  min-width: 100px;\n  transition: all 0.3s ease;\n  padding: 12px 16px;\n}\n\n.admin-tabs .MuiTab-root.Mui-selected {\n  font-weight: 600;\n  background-color: rgba(0, 0, 0, 0.03);\n}\n\n.dark-mode .admin-tabs .MuiTab-root.Mui-selected {\n  background-color: rgba(255, 255, 255, 0.05);\n}\n\n/* Dark mode toggle */\n.dark-mode-toggle {\n  position: fixed;\n  bottom: 20px;\n  right: 20px;\n  z-index: 1000;\n  width: 50px;\n  height: 50px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\n  transition: all 0.3s ease;\n}\n\n.dark-mode-toggle:hover {\n  transform: scale(1.1);\n}\n\n/* Animation for page transitions */\n.page-transition-enter {\n  opacity: 0;\n  transform: translateY(20px);\n}\n\n.page-transition-enter-active {\n  opacity: 1;\n  transform: translateY(0);\n  transition: opacity 300ms, transform 300ms;\n}\n\n.page-transition-exit {\n  opacity: 1;\n  transform: translateY(0);\n}\n\n.page-transition-exit-active {\n  opacity: 0;\n  transform: translateY(-20px);\n  transition: opacity 300ms, transform 300ms;\n}\n", "/* Mobile Dashboard Styles */\n\n/* Common card styles for mobile dashboard */\n.mobile-card {\n  border-radius: 8px;\n  background-color: #1e1e1e;\n  margin-bottom: 16px;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  padding: 24px 16px;\n  height: 220px; /* Fixed height for all cards */\n  width: 100%;\n  box-sizing: border-box;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\n}\n\n.mobile-card-icon {\n  width: 64px;\n  height: 64px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 16px;\n}\n\n.mobile-card-title {\n  font-size: 1.25rem;\n  font-weight: 600;\n  margin-bottom: 8px;\n  color: #ffffff;\n}\n\n.mobile-card-subtitle {\n  font-size: 0.875rem;\n  color: rgba(255, 255, 255, 0.7);\n  margin-bottom: 16px;\n}\n\n.mobile-card-button {\n  margin-top: auto;\n  width: 100%;\n  padding: 10px 0;\n  border-radius: 20px;\n  font-weight: 500;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  text-transform: none;\n}\n\n/* Activity card specific styles */\n.mobile-activity-card {\n  height: auto;\n  min-height: 220px;\n  padding: 16px;\n}\n\n.mobile-activity-title {\n  font-size: 1.25rem;\n  font-weight: 600;\n  margin-bottom: 16px;\n  color: #ffffff;\n  width: 100%;\n  text-align: left;\n}\n\n.mobile-activity-list {\n  width: 100%;\n  padding: 0;\n}\n\n.mobile-activity-item {\n  display: flex;\n  align-items: center;\n  padding: 12px 0;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.mobile-activity-item:last-child {\n  border-bottom: none;\n}\n\n.mobile-activity-avatar {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  margin-right: 12px;\n}\n\n.mobile-activity-content {\n  flex: 1;\n}\n\n.mobile-activity-text {\n  font-size: 0.875rem;\n  color: #ffffff;\n}\n\n.mobile-activity-subtext {\n  font-size: 0.75rem;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n/* Light mode overrides */\n.light-mode .mobile-card {\n  background-color: #ffffff;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\n}\n\n.light-mode .mobile-card-title {\n  color: #333333;\n}\n\n.light-mode .mobile-card-subtitle {\n  color: rgba(0, 0, 0, 0.6);\n}\n\n.light-mode .mobile-activity-title {\n  color: #333333;\n}\n\n.light-mode .mobile-activity-item {\n  border-bottom: 1px solid rgba(0, 0, 0, 0.1);\n}\n\n.light-mode .mobile-activity-text {\n  color: #333333;\n}\n\n.light-mode .mobile-activity-subtext {\n  color: rgba(0, 0, 0, 0.6);\n}\n\n/* Color variations for different card types */\n.mobile-card.leaderboard .mobile-card-icon {\n  background-color: rgba(33, 150, 243, 0.1);\n  color: #2196f3;\n}\n\n.mobile-card.game .mobile-card-icon {\n  background-color: rgba(255, 152, 0, 0.1);\n  color: #ff9800;\n}\n\n.mobile-card.team .mobile-card-icon {\n  background-color: rgba(76, 175, 80, 0.1);\n  color: #4caf50;\n}\n\n.mobile-card.tournament .mobile-card-icon {\n  background-color: rgba(156, 39, 176, 0.1);\n  color: #9c27b0;\n}\n\n/* Button color variations */\n.mobile-card.leaderboard .mobile-card-button {\n  background-color: #2196f3;\n  color: white;\n}\n\n.mobile-card.game .mobile-card-button {\n  background-color: #ff9800;\n  color: white;\n}\n\n.mobile-card.team .mobile-card-button {\n  background-color: #4caf50;\n  color: white;\n}\n\n.mobile-card.tournament .mobile-card-button {\n  background-color: #9c27b0;\n  color: white;\n}\n", ".admin-match-management {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px;\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n}\n\n.header {\n  text-align: center;\n  margin-bottom: 30px;\n  padding: 20px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border-radius: 10px;\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\n}\n\n.header h2 {\n  margin: 0 0 10px 0;\n  font-size: 2.2rem;\n  font-weight: 600;\n}\n\n.header p {\n  margin: 0;\n  opacity: 0.9;\n  font-size: 1.1rem;\n}\n\n.message {\n  padding: 12px 20px;\n  border-radius: 8px;\n  margin-bottom: 20px;\n  font-weight: 500;\n  text-align: center;\n  animation: slideIn 0.3s ease-out;\n}\n\n.message.success {\n  background-color: #d4edda;\n  color: #155724;\n  border: 1px solid #c3e6cb;\n}\n\n.message.error {\n  background-color: #f8d7da;\n  color: #721c24;\n  border: 1px solid #f5c6cb;\n}\n\n.tournament-selector {\n  background: white;\n  padding: 25px;\n  border-radius: 10px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\n  margin-bottom: 30px;\n}\n\n.tournament-selector label {\n  display: block;\n  margin-bottom: 10px;\n  font-weight: 600;\n  color: #333;\n  font-size: 1.1rem;\n}\n\n.tournament-selector select {\n  width: 100%;\n  padding: 12px 15px;\n  border: 2px solid #e1e5e9;\n  border-radius: 8px;\n  font-size: 1rem;\n  background-color: white;\n  transition: border-color 0.3s ease;\n}\n\n.tournament-selector select:focus {\n  outline: none;\n  border-color: #667eea;\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n}\n\n.loading {\n  text-align: center;\n  padding: 40px;\n  font-size: 1.2rem;\n  color: #666;\n  background: white;\n  border-radius: 10px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\n}\n\n.matches-container {\n  background: white;\n  padding: 25px;\n  border-radius: 10px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\n}\n\n.matches-container h3 {\n  margin: 0 0 25px 0;\n  color: #333;\n  font-size: 1.5rem;\n  font-weight: 600;\n}\n\n.matches-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));\n  gap: 20px;\n}\n\n.match-card {\n  border: 1px solid #e1e5e9;\n  border-radius: 10px;\n  padding: 20px;\n  background: #fafbfc;\n  transition: all 0.3s ease;\n  position: relative;\n}\n\n.match-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n  border-color: #667eea;\n}\n\n.match-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n  padding-bottom: 15px;\n  border-bottom: 1px solid #e1e5e9;\n}\n\n.teams {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  font-weight: 600;\n  font-size: 1.1rem;\n}\n\n.team {\n  color: #333;\n}\n\n.vs {\n  color: #666;\n  font-weight: 400;\n  font-size: 0.9rem;\n}\n\n.status-badge {\n  padding: 4px 12px;\n  border-radius: 20px;\n  font-size: 0.8rem;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n\n.status-completed {\n  background-color: #d4edda;\n  color: #155724;\n}\n\n.status-ongoing {\n  background-color: #fff3cd;\n  color: #856404;\n}\n\n.status-scheduled {\n  background-color: #d1ecf1;\n  color: #0c5460;\n}\n\n.match-details {\n  margin-bottom: 15px;\n}\n\n.detail {\n  margin-bottom: 8px;\n  color: #555;\n}\n\n.detail strong {\n  color: #333;\n  margin-right: 8px;\n}\n\n.match-result {\n  background: white;\n  padding: 15px;\n  border-radius: 8px;\n  margin-bottom: 15px;\n  border-left: 4px solid #28a745;\n}\n\n.scores {\n  margin-bottom: 10px;\n}\n\n.score {\n  margin-bottom: 5px;\n  font-family: 'Courier New', monospace;\n}\n\n.winner {\n  color: #28a745;\n  font-weight: 600;\n}\n\n.match-actions {\n  display: flex;\n  gap: 10px;\n  justify-content: flex-end;\n}\n\n.btn {\n  padding: 8px 16px;\n  border: none;\n  border-radius: 6px;\n  cursor: pointer;\n  font-weight: 500;\n  font-size: 0.9rem;\n  transition: all 0.3s ease;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n\n.btn-edit {\n  background-color: #007bff;\n  color: white;\n}\n\n.btn-edit:hover {\n  background-color: #0056b3;\n  transform: translateY(-1px);\n}\n\n.btn-delete {\n  background-color: #dc3545;\n  color: white;\n}\n\n.btn-delete:hover {\n  background-color: #c82333;\n  transform: translateY(-1px);\n}\n\n.btn-cancel {\n  background-color: #6c757d;\n  color: white;\n}\n\n.btn-cancel:hover {\n  background-color: #545b62;\n}\n\n.btn-save {\n  background-color: #28a745;\n  color: white;\n}\n\n.btn-save:hover {\n  background-color: #1e7e34;\n}\n\n.no-matches {\n  text-align: center;\n  padding: 40px;\n  color: #666;\n  background: white;\n  border-radius: 10px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\n}\n\n.modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n  animation: fadeIn 0.3s ease-out;\n}\n\n.modal {\n  background: white;\n  border-radius: 12px;\n  width: 90%;\n  max-width: 600px;\n  max-height: 90vh;\n  overflow-y: auto;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\n  animation: slideUp 0.3s ease-out;\n}\n\n.modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px 25px;\n  border-bottom: 1px solid #e1e5e9;\n  background: #f8f9fa;\n  border-radius: 12px 12px 0 0;\n}\n\n.modal-header h3 {\n  margin: 0;\n  color: #333;\n  font-size: 1.4rem;\n}\n\n.close-btn {\n  background: none;\n  border: none;\n  font-size: 1.5rem;\n  cursor: pointer;\n  color: #666;\n  padding: 0;\n  width: 30px;\n  height: 30px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 50%;\n  transition: all 0.3s ease;\n}\n\n.close-btn:hover {\n  background-color: #e9ecef;\n  color: #333;\n}\n\n.modal-body {\n  padding: 25px;\n}\n\n.form-group {\n  margin-bottom: 20px;\n}\n\n.form-group label {\n  display: block;\n  margin-bottom: 8px;\n  font-weight: 600;\n  color: #333;\n}\n\n.form-group input,\n.form-group select {\n  width: 100%;\n  padding: 10px 12px;\n  border: 2px solid #e1e5e9;\n  border-radius: 6px;\n  font-size: 1rem;\n  transition: border-color 0.3s ease;\n  box-sizing: border-box;\n}\n\n.form-group input:focus,\n.form-group select:focus {\n  outline: none;\n  border-color: #667eea;\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n}\n\n.scores-section {\n  margin-top: 25px;\n  padding-top: 20px;\n  border-top: 1px solid #e1e5e9;\n}\n\n.scores-section h4 {\n  margin: 0 0 20px 0;\n  color: #333;\n  font-size: 1.2rem;\n}\n\n.team-score {\n  margin-bottom: 20px;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 8px;\n}\n\n.team-score h5 {\n  margin: 0 0 10px 0;\n  color: #555;\n  font-size: 1rem;\n}\n\n.score-inputs {\n  display: grid;\n  grid-template-columns: 1fr 1fr 1fr;\n  gap: 10px;\n}\n\n.score-inputs input {\n  margin: 0;\n}\n\n.modal-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 10px;\n  padding: 20px 25px;\n  border-top: 1px solid #e1e5e9;\n  background: #f8f9fa;\n  border-radius: 0 0 12px 12px;\n}\n\n@keyframes slideIn {\n  from {\n    opacity: 0;\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n\n@keyframes slideUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@media (max-width: 768px) {\n  .admin-match-management {\n    padding: 15px;\n  }\n  \n  .matches-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .match-actions {\n    justify-content: center;\n  }\n  \n  .teams {\n    flex-direction: column;\n    gap: 5px;\n    text-align: center;\n  }\n  \n  .score-inputs {\n    grid-template-columns: 1fr;\n  }\n  \n  .modal {\n    width: 95%;\n    margin: 10px;\n  }\n  \n  .modal-footer {\n    flex-direction: column;\n  }\n  \n  .modal-footer .btn {\n    width: 100%;\n  }\n}", "/* Cricket Player Card Styles - FIFA Inspired */\n.cricket-player-card {\n  position: relative;\n  width: 300px;\n  height: 485px;\n  /* Using a gradient background instead of an image for better compatibility */\n  background: linear-gradient(135deg, #e6c656 0%, #a17c32 100%);\n  background-position: center center;\n  background-size: 100% 100%;\n  background-repeat: no-repeat;\n  padding: 3.8rem 0;\n  z-index: 2;\n  transition: 200ms ease-in;\n  margin: 0 auto;\n  cursor: pointer;\n  border-radius: 15px;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\n  font-family: 'Saira Semi Condensed', sans-serif;\n}\n\n/* Enhanced gold card background with shine effect */\n.cricket-player-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(135deg, #e6c656 0%, #a17c32 100%);\n  border-radius: 15px;\n  z-index: -1;\n}\n\n/* Add shine effect */\n.cricket-player-card::after {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -50%;\n  width: 200%;\n  height: 100%;\n  background: linear-gradient(\n    to right,\n    rgba(255, 255, 255, 0) 0%,\n    rgba(255, 255, 255, 0.1) 50%,\n    rgba(255, 255, 255, 0) 100%\n  );\n  transform: rotate(30deg);\n  z-index: 1;\n  pointer-events: none;\n}\n\n.cricket-player-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);\n}\n\n/* Selection checkbox */\n.cricket-player-card .player-selection {\n  position: absolute;\n  top: 12px;\n  left: 12px;\n  z-index: 10;\n  background-color: rgba(0, 0, 0, 0.6);\n  border-radius: 4px;\n  width: 32px;\n  height: 32px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);\n  transition: all 0.2s ease;\n}\n\n.cricket-player-card .player-selection:hover {\n  background-color: rgba(0, 0, 0, 0.8);\n}\n\n/* Top section of the card */\n.cricket-player-card .player-card-top {\n  position: relative;\n  display: flex;\n  color: #e9cc74;\n  padding: 0 1.5rem;\n}\n\n.cricket-player-card .player-card-top .player-master-info {\n  position: absolute;\n  line-height: 2.2rem;\n  font-weight: 300;\n  padding: 1.5rem 0;\n  text-transform: uppercase;\n}\n\n.cricket-player-card .player-card-top .player-master-info .player-rating {\n  font-size: 2.2rem;\n  font-weight: 700;\n  text-shadow: 2px 2px #111;\n}\n\n.cricket-player-card .player-card-top .player-master-info .player-position {\n  font-size: 1.8rem;\n  margin-top: 0.3rem;\n}\n\n.cricket-player-card .player-card-top .player-master-info .player-nation {\n  display: block;\n  width: 3.2rem;\n  height: 2rem;\n  margin: 0.5rem 0;\n  border-radius: 4px;\n  overflow: hidden;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  background-color: rgba(0, 0, 0, 0.2);\n}\n\n.cricket-player-card .player-card-top .player-master-info .player-nation img {\n  width: 100%;\n  height: 100%;\n  object-fit: contain;\n  padding: 2px;\n}\n\n.cricket-player-card .player-card-top .player-master-info .player-team {\n  display: block;\n  width: 2.5rem;\n  height: 40px;\n  margin-top: 0.5rem;\n  border-radius: 4px;\n  overflow: hidden;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n}\n\n.cricket-player-card .player-card-top .player-master-info .player-team img {\n  width: 100%;\n  height: 100%;\n  object-fit: contain;\n}\n\n/* Player picture */\n.cricket-player-card .player-card-top .player-picture {\n  width: 200px;\n  height: 200px;\n  margin: 0 auto;\n  overflow: hidden;\n  position: relative;\n}\n\n.cricket-player-card .player-card-top .player-picture img {\n  width: 100%;\n  height: 100%;\n  object-fit: contain;\n  position: relative;\n  right: -1.5rem;\n  bottom: 0;\n  transition: transform 0.3s ease;\n}\n\n.cricket-player-card:hover .player-card-top .player-picture img {\n  transform: scale(1.05);\n}\n\n.cricket-player-card .player-card-top .player-picture .player-extra {\n  position: absolute;\n  right: 0;\n  bottom: -0.5rem;\n  overflow: hidden;\n  font-size: 1rem;\n  font-weight: 700;\n  text-transform: uppercase;\n  width: 100%;\n  height: 2rem;\n  padding: 0 1.5rem;\n  text-align: right;\n  background: none;\n}\n\n.cricket-player-card .player-card-top .player-picture .player-extra span {\n  margin-left: 0.6rem;\n  text-shadow: 2px 2px #333;\n  background-color: rgba(0, 0, 0, 0.5);\n  padding: 2px 6px;\n  border-radius: 4px;\n}\n\n/* Bottom section of the card */\n.cricket-player-card .player-card-bottom {\n  position: relative;\n}\n\n.cricket-player-card .player-card-bottom .player-info {\n  display: block;\n  padding: 0.3rem 0;\n  color: #e9cc74;\n  width: 90%;\n  margin: 0 auto;\n  height: auto;\n  position: relative;\n  z-index: 2;\n}\n\n.cricket-player-card .player-card-bottom .player-info .player-name {\n  width: 100%;\n  display: block;\n  text-align: center;\n  font-size: 1.6rem;\n  text-transform: uppercase;\n  border-bottom: 2px solid rgba(233, 204, 116, 0.1);\n  padding-bottom: 0.3rem;\n  overflow: hidden;\n  letter-spacing: 1px;\n}\n\n.cricket-player-card .player-card-bottom .player-info .player-name span {\n  display: block;\n  text-shadow: 2px 2px #111;\n  font-weight: 700;\n}\n\n.cricket-player-card .player-card-bottom .player-info .player-features {\n  margin: 0.5rem auto;\n  display: flex;\n  justify-content: center;\n}\n\n.cricket-player-card .player-card-bottom .player-info .player-features .player-features-col {\n  border-right: 2px solid rgba(233, 204, 116, 0.1);\n  padding: 0 2.3rem;\n}\n\n.cricket-player-card .player-card-bottom .player-info .player-features .player-features-col span {\n  display: flex;\n  font-size: 1.2rem;\n  text-transform: uppercase;\n  margin-bottom: 0.5rem;\n}\n\n.cricket-player-card .player-card-bottom .player-info .player-features .player-features-col span .player-feature-value {\n  margin-right: 0.3rem;\n  font-weight: 700;\n  text-shadow: 1px 1px #111;\n}\n\n.cricket-player-card .player-card-bottom .player-info .player-features .player-features-col span .player-feature-title {\n  font-weight: 300;\n  opacity: 0.9;\n}\n\n.cricket-player-card .player-card-bottom .player-info .player-features .player-features-col:last-child {\n  border: 0;\n}\n\n/* Action buttons */\n.cricket-player-card .player-actions {\n  position: absolute;\n  top: 12px;\n  right: 12px;\n  z-index: 10;\n  display: flex;\n  gap: 8px;\n}\n\n.cricket-player-card .player-actions .edit-button,\n.cricket-player-card .player-actions .delete-button {\n  color: white;\n  background-color: rgba(0, 0, 0, 0.6);\n  width: 32px;\n  height: 32px;\n  transition: all 0.2s ease;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.cricket-player-card .player-actions .edit-button:hover {\n  background-color: #2196f3;\n  transform: translateY(-2px);\n}\n\n.cricket-player-card .player-actions .delete-button:hover {\n  background-color: #f44336;\n  transform: translateY(-2px);\n}\n\n/* Dark mode adjustments */\n@media (prefers-color-scheme: dark) {\n  .cricket-player-card {\n    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);\n  }\n\n  .cricket-player-card .player-card-top .player-picture .player-extra span {\n    background-color: rgba(0, 0, 0, 0.7);\n  }\n\n  .cricket-player-card .player-card-bottom .player-info .player-name span {\n    text-shadow: 2px 2px #000;\n  }\n\n  .cricket-player-card .player-card-bottom .player-info .player-features .player-features-col span .player-feature-value {\n    text-shadow: 1px 1px #000;\n  }\n}\n\n/* Responsive adjustments */\n@media (max-width: 768px) {\n  .cricket-player-card {\n    width: 280px;\n    height: 455px;\n  }\n\n  .cricket-player-card .player-card-top .player-master-info .player-rating {\n    font-size: 2rem;\n  }\n\n  .cricket-player-card .player-card-top .player-picture {\n    width: 180px;\n    height: 180px;\n  }\n\n  .cricket-player-card .player-card-bottom .player-info .player-features .player-features-col {\n    padding: 0 2rem;\n  }\n}\n\n@media (max-width: 576px) {\n  .cricket-player-card {\n    width: 260px;\n    height: 425px;\n  }\n\n  .cricket-player-card .player-card-top .player-master-info .player-rating {\n    font-size: 1.8rem;\n  }\n\n  .cricket-player-card .player-card-top .player-picture {\n    width: 160px;\n    height: 160px;\n  }\n\n  .cricket-player-card .player-card-bottom .player-info .player-name {\n    font-size: 1.4rem;\n  }\n\n  .cricket-player-card .player-card-bottom .player-info .player-features .player-features-col {\n    padding: 0 1.8rem;\n  }\n\n  .cricket-player-card .player-card-bottom .player-info .player-features .player-features-col span {\n    font-size: 1rem;\n  }\n}\n\n/* Mobile portrait mode */\n@media (max-width: 375px) {\n  .cricket-player-card {\n    width: 240px;\n    height: 390px;\n  }\n\n  .cricket-player-card .player-card-top .player-master-info .player-rating {\n    font-size: 1.6rem;\n  }\n\n  .cricket-player-card .player-card-top .player-master-info .player-position {\n    font-size: 1.4rem;\n  }\n\n  .cricket-player-card .player-card-top .player-picture {\n    width: 140px;\n    height: 140px;\n  }\n\n  .cricket-player-card .player-card-bottom .player-info .player-features .player-features-col {\n    padding: 0 1.4rem;\n  }\n\n  .cricket-player-card .player-card-bottom .player-info .player-features .player-features-col span {\n    font-size: 0.9rem;\n  }\n}\n", "/* CSS Variables for theming */\n:root {\n  --tb-bg-primary: #f8f9fa;\n  --tb-bg-secondary: #ffffff;\n  --tb-bg-tertiary: #f8f9fa;\n  --tb-text-primary: #2c3e50;\n  --tb-text-secondary: #6c757d;\n  --tb-border-color: #e9ecef;\n  --tb-shadow: 0 4px 20px rgba(0,0,0,0.08);\n  --tb-shadow-hover: 0 8px 25px rgba(0,0,0,0.15);\n}\n\n/* Dark mode variables */\n.theme-dark {\n  --tb-bg-primary: #111827;\n  --tb-bg-secondary: #1f2937;\n  --tb-bg-tertiary: #374151;\n  --tb-text-primary: #f3f4f6;\n  --tb-text-secondary: #d1d5db;\n  --tb-border-color: #4b5563;\n  --tb-shadow: 0 4px 20px rgba(0,0,0,0.3);\n  --tb-shadow-hover: 0 8px 25px rgba(0,0,0,0.4);\n}\n\n.template-builder {\n  padding: 24px;\n  max-width: 1800px;\n  margin: 0 auto;\n  min-height: 100vh;\n  background: var(--tb-bg-primary);\n  color: var(--tb-text-primary);\n  transition: background-color 0.3s ease, color 0.3s ease;\n}\n\n.template-builder-header {\n  text-align: center;\n  margin-bottom: 32px;\n  background: var(--tb-bg-secondary);\n  padding: 32px;\n  border-radius: 12px;\n  box-shadow: var(--tb-shadow);\n  border: 1px solid var(--tb-border-color);\n  transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;\n}\n\n.template-builder-header h2 {\n  color: var(--tb-text-primary);\n  margin-bottom: 12px;\n  font-size: 28px;\n  font-weight: 600;\n  transition: color 0.3s ease;\n}\n\n.template-builder-header p {\n  color: var(--tb-text-secondary);\n  font-size: 16px;\n  margin-bottom: 24px;\n  transition: color 0.3s ease;\n}\n\n.instructions {\n  text-align: left;\n  max-width: 800px;\n  margin: 0 auto;\n  background: var(--tb-bg-tertiary);\n  padding: 20px;\n  border-radius: 8px;\n  border-left: 4px solid #007bff;\n  transition: background-color 0.3s ease;\n}\n\n.instructions h4 {\n  color: var(--tb-text-primary);\n  margin-bottom: 12px;\n  transition: color 0.3s ease;\n}\n\n.instructions ul {\n  margin: 0 0 16px 0;\n  padding-left: 20px;\n}\n\n.instructions li {\n  margin-bottom: 8px;\n  color: var(--tb-text-secondary);\n  transition: color 0.3s ease;\n}\n\n.template-form {\n  background: var(--tb-bg-secondary);\n  padding: 24px;\n  border-radius: 12px;\n  margin-bottom: 32px;\n  display: grid;\n  grid-template-columns: 1fr 1fr 1fr;\n  gap: 24px;\n  box-shadow: var(--tb-shadow);\n  border: 1px solid var(--tb-border-color);\n  transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;\n}\n\n.form-group {\n  display: flex;\n  flex-direction: column;\n}\n\n.form-group label {\n  font-weight: 600;\n  margin-bottom: 8px;\n  color: var(--tb-text-primary);\n  transition: color 0.3s ease;\n}\n\n.form-group input,\n.form-group textarea {\n  padding: 12px 16px;\n  border: 2px solid var(--tb-border-color);\n  border-radius: 8px;\n  font-size: 14px;\n  background: var(--tb-bg-secondary);\n  color: var(--tb-text-primary);\n  transition: border-color 0.2s, background-color 0.3s ease, color 0.3s ease;\n}\n\n.form-group input:focus,\n.form-group textarea:focus {\n  outline: none;\n  border-color: #007bff;\n  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);\n}\n\n/* Dark mode specific input styling */\n.theme-dark .form-group input,\n.theme-dark .form-group textarea {\n  background: var(--tb-bg-tertiary);\n}\n\n.theme-dark .form-group input::placeholder,\n.theme-dark .form-group textarea::placeholder {\n  color: var(--tb-text-secondary);\n}\n\n.form-group textarea {\n  resize: vertical;\n  min-height: 60px;\n}\n\n.template-builder-content {\n  display: grid;\n  grid-template-columns: 1fr 480px;\n  gap: 32px;\n  align-items: start;\n  min-height: 600px;\n}\n\n.canvas-container {\n  background: var(--tb-bg-secondary);\n  border-radius: 12px;\n  padding: 24px;\n  box-shadow: var(--tb-shadow);\n  border: 1px solid var(--tb-border-color);\n  min-height: 500px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;\n}\n\n.controls-panel {\n  background: var(--tb-bg-secondary);\n  border-radius: 12px;\n  padding: 24px;\n  box-shadow: var(--tb-shadow);\n  height: fit-content;\n  border: 1px solid var(--tb-border-color);\n  transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;\n}\n\n.field-types h3 {\n  margin-bottom: 15px;\n  color: var(--tb-text-primary);\n  font-size: 16px;\n  transition: color 0.3s ease;\n}\n\n.field-type-grid {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 10px;\n  margin-bottom: 32px;\n}\n\n.field-type-btn {\n  display: flex;\n  align-items: center;\n  padding: 12px 16px;\n  border: 2px solid var(--tb-border-color);\n  background: var(--tb-bg-secondary);\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.2s;\n  font-size: 14px;\n  text-align: left;\n  font-weight: 500;\n  color: var(--tb-text-primary);\n}\n\n.field-type-btn:hover {\n  background: var(--tb-bg-tertiary);\n  transform: translateY(-1px);\n}\n\n.field-type-btn.active {\n  background: #007bff;\n  border-color: #007bff;\n  color: white;\n  font-weight: 600;\n}\n\n.color-indicator {\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n  margin-right: 8px;\n  flex-shrink: 0;\n}\n\n.regions-list h3 {\n  margin-bottom: 15px;\n  color: var(--tb-text-primary);\n  font-size: 16px;\n  transition: color 0.3s ease;\n}\n\n.regions-container {\n  max-height: 300px;\n  overflow-y: auto;\n  border: 1px solid var(--tb-border-color);\n  border-radius: 4px;\n  margin-bottom: 20px;\n  background: var(--tb-bg-secondary);\n  transition: border-color 0.3s ease, background-color 0.3s ease;\n}\n\n.region-item {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 10px 12px;\n  border-bottom: 1px solid var(--tb-border-color);\n  transition: background 0.2s;\n}\n\n.region-item:hover {\n  background: var(--tb-bg-tertiary);\n}\n\n.region-item:last-child {\n  border-bottom: none;\n}\n\n.region-info {\n  display: flex;\n  align-items: center;\n  flex: 1;\n}\n\n.region-info span {\n  font-weight: 500;\n  margin-right: 8px;\n  color: var(--tb-text-primary);\n  transition: color 0.3s ease;\n}\n\n.region-info small {\n  color: var(--tb-text-secondary);\n  font-size: 11px;\n  transition: color 0.3s ease;\n}\n\n.delete-btn {\n  background: #ff4757;\n  color: white;\n  border: none;\n  border-radius: 50%;\n  width: 24px;\n  height: 24px;\n  cursor: pointer;\n  font-size: 16px;\n  line-height: 1;\n  transition: background 0.2s;\n}\n\n.delete-btn:hover {\n  background: #ff3742;\n}\n\n.template-status {\n  margin-bottom: 15px;\n  padding: 15px;\n  background: var(--tb-bg-tertiary);\n  border-radius: 6px;\n  border: 1px solid var(--tb-border-color);\n  transition: background-color 0.3s ease, border-color 0.3s ease;\n}\n\n.status-indicators {\n  display: flex;\n  gap: 15px;\n  flex-wrap: wrap;\n}\n\n.status-indicator {\n  padding: 8px 12px;\n  border-radius: 4px;\n  font-size: 13px;\n  font-weight: 600;\n  transition: all 0.2s;\n}\n\n.status-indicator.valid {\n  background: #d4edda;\n  color: #155724;\n  border: 1px solid #c3e6cb;\n}\n\n.status-indicator.invalid {\n  background: #f8d7da;\n  color: #721c24;\n  border: 1px solid #f5c6cb;\n}\n\n.template-actions {\n  display: flex;\n  gap: 10px;\n}\n\n.save-btn,\n.clear-btn,\n.manage-btn,\n.reset-btn {\n  flex: 1;\n  padding: 12px 20px;\n  border: none;\n  border-radius: 6px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s;\n  font-size: 14px;\n}\n\n.save-btn {\n  background: #27ae60;\n  color: white;\n}\n\n.save-btn:hover:not(:disabled) {\n  background: #229954;\n  transform: translateY(-1px);\n}\n\n.save-btn:disabled {\n  background: #95a5a6;\n  cursor: not-allowed;\n  transform: none;\n}\n\n.clear-btn {\n  background: #e74c3c;\n  color: white;\n}\n\n.clear-btn:hover {\n  background: #c0392b;\n  transform: translateY(-1px);\n}\n\n.manage-btn {\n  background: #3498db;\n  color: white;\n}\n\n.manage-btn:hover {\n  background: #2980b9;\n  transform: translateY(-1px);\n}\n\n.reset-btn {\n  background: #f39c12;\n  color: white;\n}\n\n.reset-btn:hover {\n  background: #e67e22;\n  transform: translateY(-1px);\n}\n\n/* Template List Styles */\n.template-list {\n  margin-top: 20px;\n  padding: 20px;\n  background: var(--tb-bg-tertiary);\n  border-radius: 8px;\n  border: 1px solid var(--tb-border-color);\n  transition: background-color 0.3s ease, border-color 0.3s ease;\n}\n\n.template-list h3 {\n  margin: 0 0 15px 0;\n  color: var(--tb-text-primary);\n  font-size: 18px;\n  transition: color 0.3s ease;\n}\n\n.templates-grid {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 12px;\n  max-height: 400px;\n  overflow-y: auto;\n}\n\n.template-card {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15px;\n  background: var(--tb-bg-secondary);\n  border-radius: 6px;\n  border: 1px solid var(--tb-border-color);\n  transition: all 0.2s;\n}\n\n.template-card:hover {\n  border-color: #007bff;\n  box-shadow: var(--tb-shadow-hover);\n}\n\n.template-info {\n  flex: 1;\n}\n\n.template-info h4 {\n  margin: 0 0 5px 0;\n  color: var(--tb-text-primary);\n  font-size: 16px;\n  transition: color 0.3s ease;\n}\n\n.template-info p {\n  margin: 0 0 5px 0;\n  color: var(--tb-text-secondary);\n  font-size: 14px;\n  transition: color 0.3s ease;\n}\n\n.template-info small {\n  color: var(--tb-text-secondary);\n  font-size: 12px;\n  transition: color 0.3s ease;\n}\n\n.template-actions-small {\n  display: flex;\n  gap: 8px;\n}\n\n.load-btn,\n.test-btn,\n.delete-btn-small {\n  padding: 8px 12px;\n  border: none;\n  border-radius: 4px;\n  font-size: 12px;\n  cursor: pointer;\n  transition: all 0.2s;\n}\n\n.load-btn {\n  background: #28a745;\n  color: white;\n}\n\n.load-btn:hover {\n  background: #218838;\n}\n\n.test-btn {\n  background: #17a2b8;\n  color: white;\n}\n\n.test-btn:hover {\n  background: #138496;\n}\n\n.delete-btn-small {\n  background: #dc3545;\n  color: white;\n}\n\n.delete-btn-small:hover {\n  background: #c82333;\n}\n\n/* Responsive Design */\n@media (max-width: 1400px) {\n  .template-builder-content {\n    grid-template-columns: 1fr 420px;\n    gap: 24px;\n  }\n}\n\n@media (max-width: 1200px) {\n  .template-builder {\n    padding: 20px;\n  }\n\n  .template-builder-content {\n    grid-template-columns: 1fr;\n    gap: 24px;\n  }\n\n  .controls-panel {\n    order: -1;\n    padding: 20px;\n  }\n\n  .field-type-grid {\n    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));\n    gap: 12px;\n  }\n}\n\n@media (max-width: 768px) {\n  .template-builder {\n    padding: 16px;\n  }\n\n  .template-form {\n    grid-template-columns: 1fr;\n    gap: 16px;\n    padding: 16px;\n  }\n\n  .field-type-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .template-actions {\n    flex-direction: column;\n    gap: 12px;\n  }\n\n  .controls-panel {\n    padding: 16px;\n  }\n\n  .canvas-container {\n    padding: 16px;\n  }\n}\n\n/* Canvas styling */\ncanvas {\n  max-width: 100%;\n  height: auto;\n  display: block;\n  transition: cursor 0.1s ease;\n  border: 2px solid var(--tb-border-color);\n  border-radius: 4px;\n  background: var(--tb-bg-secondary);\n}\n\ncanvas:hover {\n  border-color: #007bff;\n}\n\n/* Scrollbar styling for regions list */\n.regions-container::-webkit-scrollbar {\n  width: 6px;\n}\n\n.regions-container::-webkit-scrollbar-track {\n  background: var(--tb-bg-tertiary);\n  border-radius: 3px;\n}\n\n.regions-container::-webkit-scrollbar-thumb {\n  background: var(--tb-border-color);\n  border-radius: 3px;\n}\n\n.regions-container::-webkit-scrollbar-thumb:hover {\n  background: var(--tb-text-secondary);\n}\n\n/* Dark mode specific scrollbar styling */\n.theme-dark .regions-container::-webkit-scrollbar-track {\n  background: var(--tb-bg-tertiary);\n}\n\n.theme-dark .regions-container::-webkit-scrollbar-thumb {\n  background: #6b7280;\n}\n\n.theme-dark .regions-container::-webkit-scrollbar-thumb:hover {\n  background: #9ca3af;\n}\n\n/* Instructions */\n.instructions {\n  background: #e8f4fd;\n  border: 1px solid #bee5eb;\n  border-radius: 6px;\n  padding: 15px;\n  margin: 20px 0;\n  text-align: left;\n}\n\n.instructions h4 {\n  margin: 0 0 10px 0;\n  color: #0c5460;\n}\n\n.instructions ul {\n  margin: 0 0 10px 0;\n  padding-left: 20px;\n  color: #0c5460;\n}\n\n.instructions li {\n  margin-bottom: 5px;\n}\n\n.instructions p {\n  margin: 10px 0 0 0;\n  color: #0c5460;\n  font-style: italic;\n}\n\n/* Canvas Controls */\n.canvas-controls {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15px;\n  background: var(--tb-bg-tertiary);\n  border-radius: 6px;\n  margin-bottom: 15px;\n  border: 1px solid var(--tb-border-color);\n  transition: background-color 0.3s ease, border-color 0.3s ease;\n}\n\n.mapping-mode,\n.zoom-controls {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.mapping-mode label,\n.zoom-controls label {\n  font-weight: 600;\n  color: var(--tb-text-primary);\n  margin-right: 5px;\n  transition: color 0.3s ease;\n}\n\n.mode-buttons,\n.zoom-buttons {\n  display: flex;\n  gap: 5px;\n}\n\n.mode-btn,\n.zoom-buttons button {\n  padding: 8px 12px;\n  border: 1px solid var(--tb-border-color);\n  background: var(--tb-bg-secondary);\n  border-radius: 4px;\n  cursor: pointer;\n  transition: all 0.2s;\n  font-size: 12px;\n  color: var(--tb-text-primary);\n}\n\n.mode-btn:hover,\n.zoom-buttons button:hover {\n  background: var(--tb-bg-tertiary);\n  border-color: var(--tb-border-color);\n}\n\n.mode-btn.active {\n  background: #007bff;\n  color: white;\n  border-color: #007bff;\n}\n\n.zoom-buttons span {\n  padding: 8px 12px;\n  background: var(--tb-bg-tertiary);\n  border-radius: 4px;\n  font-weight: 600;\n  min-width: 50px;\n  text-align: center;\n  color: var(--tb-text-primary);\n  border: 1px solid var(--tb-border-color);\n  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;\n}\n\n.canvas-wrapper {\n  overflow: auto;\n  max-height: 600px;\n  border: 1px solid var(--tb-border-color);\n  border-radius: 4px;\n  transition: border-color 0.3s ease;\n}\n\n.mapping-help {\n  margin-top: 10px;\n  padding: 10px;\n  background: var(--tb-bg-tertiary);\n  border: 1px solid var(--tb-border-color);\n  border-radius: 4px;\n  font-size: 12px;\n  transition: background-color 0.3s ease, border-color 0.3s ease;\n}\n\n.mapping-help p {\n  margin: 2px 0;\n  color: var(--tb-text-secondary);\n  transition: color 0.3s ease;\n}\n\n.selected-region-info {\n  margin-top: 10px;\n  padding: 10px;\n  background: var(--tb-bg-tertiary);\n  border: 1px solid var(--tb-border-color);\n  border-radius: 4px;\n  transition: background-color 0.3s ease, border-color 0.3s ease;\n}\n\n.selected-region-info h4 {\n  margin: 0 0 5px 0;\n  color: var(--tb-text-primary);\n  font-size: 14px;\n  transition: color 0.3s ease;\n}\n\n.selected-region-info p {\n  margin: 3px 0;\n  color: var(--tb-text-secondary);\n  font-size: 11px;\n  transition: color 0.3s ease;\n}\n", "/* Auction Player Card Styles - FIFA Inspired */\n.cricket-player-card.auction-card {\n  position: relative;\n  width: 100%;\n  max-width: 300px;\n  height: 470px; /* Increased height to accommodate bid button */\n  /* Using a gradient background instead of an image for better compatibility */\n  background: linear-gradient(135deg, #e6c656 0%, #a17c32 100%);\n  background-position: center center;\n  background-size: 100% 100%;\n  background-repeat: no-repeat;\n  padding: 2.5rem 0;\n  z-index: 2;\n  transition: 200ms ease-in;\n  margin: 0 auto;\n  margin-bottom: 25px; /* Increased bottom margin to prevent overlap */\n  cursor: pointer;\n  border-radius: 15px;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\n  font-family: 'Saira Semi Condensed', sans-serif;\n}\n\n/* Enhanced gold card background with shine effect */\n.cricket-player-card.auction-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(135deg, #e6c656 0%, #a17c32 100%);\n  border-radius: 15px;\n  z-index: -1;\n}\n\n/* Add shine effect */\n.cricket-player-card.auction-card::after {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -50%;\n  width: 200%;\n  height: 100%;\n  background: linear-gradient(\n    to right,\n    rgba(255, 255, 255, 0) 0%,\n    rgba(255, 255, 255, 0.1) 50%,\n    rgba(255, 255, 255, 0) 100%\n  );\n  transform: rotate(30deg);\n  z-index: 1;\n  pointer-events: none;\n}\n\n.cricket-player-card.auction-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);\n}\n\n/* Player card top section */\n.cricket-player-card.auction-card .player-card-top {\n  height: 50%;\n  width: 100%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  z-index: 2;\n  position: relative;\n}\n\n/* Player master info (rating, position, nation) */\n.cricket-player-card.auction-card .player-card-top .player-master-info {\n  width: 100%;\n  display: flex;\n  flex-direction: column; /* Changed to vertical layout */\n  align-items: flex-start; /* Align items to the left */\n  padding: 0;\n  margin-bottom: 0.3rem;\n  position: relative;\n  z-index: 5; /* Higher z-index to ensure it's above the player image */\n  position: absolute; /* Position absolutely */\n  top: 10px; /* Position from top */\n  left: 25px; /* Adjusted to better align with player type and flag */\n}\n\n/* Removed vertical line effect */\n\n.cricket-player-card.auction-card .player-card-top .player-master-info .player-rating {\n  width: 2.5rem;\n  height: 2.5rem;\n  font-size: 2rem;\n  font-weight: 700;\n  display: flex;\n  align-items: center;\n  justify-content: flex-start;\n  margin-bottom: 0.5rem;\n  padding-left: 0; /* Removed padding to better align with player type and flag */\n  position: relative;\n  z-index: 2; /* Ensure it's above the vertical line */\n  text-shadow: 0 0 5px rgba(255, 255, 255, 0.5); /* Add glow effect */\n}\n\n.cricket-player-card.auction-card .player-card-top .player-master-info .player-position {\n  width: 2.5rem;\n  height: 2.5rem;\n  font-size: 1.2rem;\n  font-weight: 700;\n  color: #fff;\n  display: flex;\n  align-items: center;\n  justify-content: flex-start;\n  position: relative;\n  z-index: 3; /* Higher z-index to ensure text is above the image */\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8), 0 0 5px rgba(255, 255, 255, 0.5); /* Enhanced text shadow */\n  margin-bottom: 0.5rem;\n  padding-left: 0; /* Ensure alignment with rating */\n}\n\n.cricket-player-card.auction-card .player-card-top .player-master-info .player-nation {\n  width: 2.5rem;\n  height: 2.5rem;\n  display: flex;\n  align-items: center;\n  justify-content: flex-start;\n  padding-left: 0; /* Ensure alignment with rating and position */\n}\n\n.cricket-player-card.auction-card .player-card-top .player-master-info .player-nation img {\n  width: 1.8rem;\n  height: 1.8rem;\n  border-radius: 50%;\n  object-fit: cover;\n  box-shadow: 0 0 5px rgba(255, 255, 255, 0.5); /* Add glow effect */\n  z-index: 3;\n}\n\n/* Player picture */\n.cricket-player-card.auction-card .player-card-top .player-picture {\n  width: 180px;\n  height: 160px;\n  position: relative;\n  margin: 20px auto 0; /* Added top margin to move image down */\n  overflow: hidden;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1; /* Lower z-index to prevent overlapping with text */\n}\n\n.cricket-player-card.auction-card .player-card-top .player-picture img {\n  width: 160px;\n  height: 160px;\n  object-fit: contain; /* Changed to contain to prevent cropping */\n  object-position: center center; /* Center the image */\n  border-radius: 10px;\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);\n}\n\n/* Player name directly on card background */\n.cricket-player-card.auction-card .player-name-direct {\n  width: 100%;\n  display: flex;\n  justify-content: center;\n  margin-bottom: 3px;\n  z-index: 5;\n  position: relative;\n  margin-top: -8px; /* Move up more */\n}\n\n.cricket-player-card.auction-card .player-name-direct span {\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: #fff;\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);\n  letter-spacing: 0.5px;\n  white-space: nowrap;\n  display: block;\n  text-overflow: ellipsis;\n  overflow: hidden;\n  max-width: 95%;\n  text-align: center;\n}\n\n/* Player card bottom section */\n.cricket-player-card.auction-card .player-card-bottom {\n  height: 50%;\n  width: 100%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  z-index: 2;\n  position: relative;\n  padding-top: 0; /* Remove top padding */\n}\n\n/* Player info */\n.cricket-player-card.auction-card .player-card-bottom .player-info {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 0.3rem 0; /* Further reduced padding */\n}\n\n/* Player name */\n.cricket-player-card.auction-card .player-card-bottom .player-info .player-name {\n  width: 100%;\n  text-align: center;\n  margin-bottom: 0.5rem;\n}\n\n/* Player name at the top of player-card-bottom */\n.cricket-player-card.auction-card .player-card-bottom .player-info .player-name-top {\n  width: 100%;\n  text-align: center;\n  margin-bottom: 0.5rem;\n  padding: 0.5rem 1rem;\n  background-color: rgba(0, 0, 0, 0.5);\n  border-radius: 5px;\n}\n\n.cricket-player-card.auction-card .player-card-bottom .player-info .player-name span {\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: #fff;\n  text-shadow: 1px 1px #000;\n}\n\n/* Player info grid */\n.cricket-player-card.auction-card .player-card-bottom .player-info .player-info-grid {\n  width: 100%;\n  display: flex;\n  flex-direction: column;\n  margin: 0.2rem 0;\n  padding: 0 1rem;\n}\n\n.cricket-player-card.auction-card .player-card-bottom .player-info .player-info-grid .info-row {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 0.2rem;\n}\n\n.cricket-player-card.auction-card .player-card-bottom .player-info .player-info-grid .info-cell {\n  display: flex;\n  align-items: center;\n  width: 48%;\n}\n\n.cricket-player-card.auction-card .player-card-bottom .player-info .player-info-grid .info-label {\n  font-size: 0.8rem;\n  font-weight: 700;\n  color: #fff;\n  margin-right: 0.3rem;\n}\n\n.cricket-player-card.auction-card .player-card-bottom .player-info .player-info-grid .info-value {\n  font-size: 0.8rem;\n  color: #fff;\n}\n\n/* Auction specific styles */\n.cricket-player-card.auction-card .auction-status {\n  position: absolute;\n  top: 10px;\n  right: 10px;\n  z-index: 10;\n}\n\n.cricket-player-card.auction-card .your-bid-indicator {\n  position: absolute;\n  top: 10px;\n  left: 10px;\n  z-index: 10;\n}\n\n.cricket-player-card.auction-card .admin-controls {\n  position: absolute;\n  top: 40px;\n  right: 10px;\n  z-index: 100; /* Increased z-index to ensure it's above other elements */\n  display: flex;\n  pointer-events: auto; /* Ensure pointer events work */\n}\n\n.cricket-player-card.auction-card .auction-info {\n  width: 100%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin-top: 0.2rem;\n  padding: 0 1rem;\n  margin-bottom: 0.3rem; /* Reduced bottom margin */\n}\n\n.cricket-player-card.auction-card .auction-info .current-bid {\n  width: 100%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin-bottom: 0.3rem;\n  background-color: rgba(0, 0, 0, 0.2);\n  padding: 0.3rem;\n  border-radius: 8px;\n}\n\n.cricket-player-card.auction-card .auction-info .current-bid .bid-label {\n  font-size: 0.8rem;\n  color: #fff;\n  font-weight: 600;\n}\n\n.cricket-player-card.auction-card .auction-info .current-bid .bid-amount {\n  font-size: 1.3rem;\n  font-weight: 700;\n  color: #fff;\n}\n\n.cricket-player-card.auction-card .auction-info .current-bid .bidder-info {\n  font-size: 0.7rem;\n  color: #fff;\n  opacity: 0.9;\n}\n\n.cricket-player-card.auction-card .auction-info .time-left {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.3rem;\n  color: #fff;\n  font-size: 0.8rem;\n  margin-top: 0.3rem;\n  background-color: rgba(0, 0, 0, 0.2);\n  padding: 0.3rem 0.5rem;\n  border-radius: 8px;\n  width: 100%;\n  box-sizing: border-box;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.cricket-player-card.auction-card .auction-actions {\n  position: relative;\n  width: 100%;\n  padding: 0 1rem;\n  z-index: 10;\n  margin-top: auto;\n  margin-bottom: 15px; /* Increased bottom margin */\n  padding-top: 5px; /* Added top padding */\n}\n\n.cricket-player-card.auction-card .auction-actions button {\n  background: linear-gradient(135deg, #3a8dff 0%, #0d47a1 100%);\n  transition: all 0.2s ease;\n  font-weight: 600;\n}\n\n.cricket-player-card.auction-card .auction-actions button:hover:not(:disabled) {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\n}\n\n.cricket-player-card.auction-card .auction-actions button:disabled {\n  background: linear-gradient(135deg, #9e9e9e 0%, #616161 100%);\n  opacity: 0.8;\n}\n\n/* Responsive adjustments */\n@media (max-width: 768px) {\n  .cricket-player-card.auction-card {\n    width: 100%;\n    max-width: 280px;\n    height: 440px; /* Increased height for medium screens */\n    margin-bottom: 25px; /* Maintain bottom margin on medium screens */\n  }\n\n  .cricket-player-card.auction-card .player-card-top .player-master-info {\n    left: 20px; /* Adjust left position for medium screens */\n  }\n\n  .cricket-player-card.auction-card .player-card-top .player-master-info .player-rating {\n    font-size: 1.8rem;\n  }\n\n  .cricket-player-card.auction-card .player-card-top .player-picture {\n    width: 180px;\n    height: 160px;\n    margin: 20px auto 0; /* Maintain top margin in responsive view */\n  }\n\n  .cricket-player-card.auction-card .player-card-top .player-picture img {\n    width: 160px;\n    height: 160px;\n    object-fit: contain; /* Maintain contain to prevent cropping */\n  }\n\n  /* Responsive player name */\n  .cricket-player-card.auction-card .player-name-direct span {\n    font-size: 1.3rem;\n    letter-spacing: 0.3px;\n  }\n\n  .cricket-player-card.auction-card .player-card-bottom .player-info .player-info-grid .info-label,\n  .cricket-player-card.auction-card .player-card-bottom .player-info .player-info-grid .info-value {\n    font-size: 0.75rem;\n  }\n}\n\n@media (max-width: 576px) {\n  .cricket-player-card.auction-card {\n    width: 100%;\n    max-width: 260px;\n    height: 420px; /* Increased height for small screens */\n    margin-bottom: 25px; /* Maintain bottom margin on small screens */\n  }\n\n  .cricket-player-card.auction-card .player-card-top .player-master-info {\n    left: 15px; /* Adjust left position for small screens */\n  }\n\n  .cricket-player-card.auction-card .player-card-top .player-master-info .player-rating {\n    font-size: 1.6rem;\n  }\n\n  .cricket-player-card.auction-card .player-card-top .player-picture {\n    width: 160px;\n    height: 140px;\n    margin: 20px auto 0; /* Maintain top margin in responsive view */\n  }\n\n  .cricket-player-card.auction-card .player-card-top .player-picture img {\n    width: 140px;\n    height: 140px;\n    object-fit: contain; /* Maintain contain to prevent cropping */\n  }\n\n  /* Responsive player name for small screens */\n  .cricket-player-card.auction-card .player-name-direct {\n    margin-bottom: 8px;\n  }\n\n  .cricket-player-card.auction-card .player-name-direct span {\n    font-size: 1.1rem;\n    letter-spacing: 0px; /* Remove letter spacing on small screens */\n    max-width: 95%;\n  }\n\n  .cricket-player-card.auction-card .player-card-bottom .player-info .player-name span {\n    font-size: 1.2rem;\n  }\n\n  .cricket-player-card.auction-card .player-card-bottom .player-info .player-info-grid {\n    margin: 0.3rem 0;\n  }\n\n  .cricket-player-card.auction-card .player-card-bottom .player-info .player-info-grid .info-row {\n    margin-bottom: 0.3rem;\n  }\n\n  .cricket-player-card.auction-card .player-card-bottom .player-info .player-info-grid .info-label,\n  .cricket-player-card.auction-card .player-card-bottom .player-info .player-info-grid .info-value {\n    font-size: 0.7rem;\n  }\n\n  .cricket-player-card.auction-card .auction-info .current-bid .bid-amount {\n    font-size: 1.1rem;\n  }\n}\n"], "names": [], "sourceRoot": ""}