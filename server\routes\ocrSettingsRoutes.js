const express = require('express');
const router = express.Router();
const OCRSettings = require('../models/OCRSettings');
const { requireAdmin } = require('../middlewares/adminAuth');
const { authenticateToken } = require('../middlewares/auth');

// Get current OCR settings
router.get('/ocr-settings', [authenticateToken, requireAdmin], async (req, res) => {
  try {
    let settings = await OCRSettings.findOne();
    if (!settings) {
      settings = await OCRSettings.create({});
    }
    res.json(settings);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Update OCR settings
router.put('/ocr-settings', [authenticateToken, requireAdmin], async (req, res) => {
  try {
    const { primaryMethod, fallbackMethod, ocrSpaceTimeout } = req.body;
    let settings = await OCRSettings.findOne();
    if (!settings) {
      settings = new OCRSettings();
    }
    
    settings.primaryMethod = primaryMethod;
    settings.fallbackMethod = fallbackMethod;
    settings.ocrSpaceTimeout = ocrSpaceTimeout;
    settings.lastUpdated = new Date();
    
    await settings.save();
    res.json(settings);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;