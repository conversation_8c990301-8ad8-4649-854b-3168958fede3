const mongoose = require('mongoose');
require('dotenv').config();

async function fixExistingMatches() {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/cricket24');
    console.log('Connected to database');
    
    // Find the tournament
    const Tournament = require('./models/Tournament');
    const tournament = await Tournament.findById('681e581dfe3bc0a0414dd937');
    
    if (!tournament) {
      console.log('Tournament not found');
      return;
    }
    
    let fixedCount = 0;
    let totalMatches = 0;
    
    console.log('🔧 Fixing existing matches...');
    
    // Loop through all phases and matches
    for (let phaseIndex = 0; phaseIndex < tournament.phases.length; phaseIndex++) {
      const phase = tournament.phases[phaseIndex];
      
      for (let matchIndex = 0; matchIndex < phase.matches.length; matchIndex++) {
        const match = phase.matches[matchIndex];
        totalMatches++;
        
        let needsFix = false;
        
        // Check if dispute field is missing or invalid
        if (!match.result.dispute || typeof match.result.dispute !== 'object') {
          console.log(`🔧 Fixing dispute field for match ${match._id}`);
          tournament.phases[phaseIndex].matches[matchIndex].result.dispute = {
            isDisputed: false,
            disputedBy: null,
            disputeReason: '',
            disputeStatus: 'open',
            disputeResolution: ''
          };
          needsFix = true;
        }
        
        // Check if extractedData field is missing or invalid
        if (!match.result.extractedData || typeof match.result.extractedData !== 'object') {
          console.log(`🔧 Fixing extractedData field for match ${match._id}`);
          tournament.phases[phaseIndex].matches[matchIndex].result.extractedData = {
            isExtracted: false,
            extractionMethod: 'none',
            extractedAt: null,
            rawData: ''
          };
          needsFix = true;
        }
        
        // Ensure scorecardImages is an array
        if (!Array.isArray(match.result.scorecardImages)) {
          console.log(`🔧 Fixing scorecardImages field for match ${match._id}`);
          tournament.phases[phaseIndex].matches[matchIndex].result.scorecardImages = [];
          needsFix = true;
        }
        
        // Ensure playerPerformances is an array
        if (!Array.isArray(match.result.playerPerformances)) {
          console.log(`🔧 Fixing playerPerformances field for match ${match._id}`);
          tournament.phases[phaseIndex].matches[matchIndex].result.playerPerformances = [];
          needsFix = true;
        }
        
        if (needsFix) {
          fixedCount++;
        }
      }
    }
    
    if (fixedCount > 0) {
      console.log(`💾 Saving tournament with ${fixedCount} fixed matches...`);
      await tournament.save();
      console.log('✅ Tournament saved successfully');
    }
    
    console.log(`\n📊 SUMMARY:`);
    console.log(`Total matches: ${totalMatches}`);
    console.log(`Fixed matches: ${fixedCount}`);
    console.log(`Matches that were already correct: ${totalMatches - fixedCount}`);
    
    if (fixedCount > 0) {
      console.log('\n✅ All existing matches have been fixed!');
      console.log('🧪 You can now test adding a new match - the validation errors should be gone.');
    } else {
      console.log('\n✅ All matches were already in correct format!');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

fixExistingMatches();
