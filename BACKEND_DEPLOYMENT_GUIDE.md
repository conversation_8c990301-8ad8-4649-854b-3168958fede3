# 🚀 Backend Deployment Guide for RPL Cricket App

## 📋 **Current Status**
- ✅ **Frontend**: Deployed (or being deployed) on Dokploy
- ❌ **Backend**: **NOT DEPLOYED YET** - Needs separate deployment

## 🏗️ **Backend Architecture**
- **Framework**: Node.js + Express.js
- **Database**: MongoDB (requires external database)
- **Port**: 5000 (configurable via PORT env var)
- **Entry Point**: `server/index.js`
- **Dependencies**: 30+ packages including MongoDB, JWT, OCR services

## 🔧 **Deployment Options**

### **Option 1: Deploy on Same Dokploy Instance (Recommended)**

#### **Step 1: Create New Application in Dokploy**
1. Go to your Dokploy dashboard
2. Click "Create Application"
3. Name: `rpl-cricket-backend`
4. Source: Same GitHub repository (`rhingonekar/rplwebapp`)

#### **Step 2: Configure Application Settings**
- **Source Directory**: `server`
- **Build Command**: `npm ci`
- **Start Command**: `npm start`
- **Port**: `5000`
- **Health Check**: `/api/test`

#### **Step 3: Set Environment Variables**
```bash
NODE_ENV=production
PORT=5000
MONGODB_URI=mongodb+srv://username:<EMAIL>/rpl-cricket
JWT_SECRET=your-super-secret-jwt-key-here-make-it-long-and-random
FRONTEND_URL=https://your-frontend-domain.com
GOOGLE_APPLICATION_CREDENTIALS=/app/config/google-cloud-credentials.json
```

### **Option 2: Deploy on Separate Platform**
- **Railway**: Easy Node.js deployment
- **Render**: Free tier available
- **Heroku**: Classic choice
- **DigitalOcean App Platform**: Good performance

## 🗄️ **Database Setup (CRITICAL)**

### **MongoDB Atlas (Recommended)**
1. Go to [MongoDB Atlas](https://cloud.mongodb.com/)
2. Create free cluster
3. Create database user
4. Whitelist IP addresses (or use 0.0.0.0/0 for all)
5. Get connection string
6. Add to `MONGODB_URI` environment variable

### **Self-Hosted MongoDB**
- Install MongoDB on your server
- Configure authentication
- Set up proper firewall rules

## 🔐 **Required Environment Variables**

### **Essential (App won't work without these):**
```bash
MONGODB_URI=mongodb+srv://...
JWT_SECRET=your-secret-key
NODE_ENV=production
PORT=5000
```

### **Optional (Features may not work):**
```bash
GOOGLE_APPLICATION_CREDENTIALS=./config/google-cloud-credentials.json
OCR_SPACE_API_KEY=your-ocr-api-key
FRONTEND_URL=https://your-frontend-domain.com
```

## 🔗 **Frontend-Backend Connection**

### **Update Frontend Environment**
In your frontend deployment, set:
```bash
REACT_APP_API_URL=https://your-backend-domain.com/api
```

### **CORS Configuration**
Backend automatically handles CORS, but ensure `FRONTEND_URL` is set correctly.

## 🧪 **Testing Deployment**

### **Health Check Endpoints:**
- `GET /api/test` - Basic server test
- `GET /api/auth/test` - Database connection test
- `GET /api/tournaments` - API functionality test

### **Expected Response:**
```json
{
  "message": "Server is running!",
  "timestamp": "2025-01-17T10:30:00.000Z",
  "environment": "production"
}
```

## 🚨 **Common Issues & Solutions**

### **1. Database Connection Fails**
- Check `MONGODB_URI` format
- Verify database user permissions
- Check IP whitelist in MongoDB Atlas

### **2. OCR Features Don't Work**
- Upload Google Cloud credentials file
- Set `GOOGLE_APPLICATION_CREDENTIALS` path
- Verify OCR API keys

### **3. File Uploads Fail**
- Check disk space on server
- Verify upload directory permissions
- Check `MAX_FILE_SIZE` setting

## 📁 **Files Created for Deployment**
- ✅ `server/Procfile` - Process definition
- ✅ `server/dokploy.json` - Dokploy configuration
- ✅ `server/.dokploy/build.sh` - Build script
- ✅ `server/.dokploy/start.sh` - Start script
- ✅ `server/.env.example` - Environment template

## 🎯 **Next Steps**
1. **Set up MongoDB database** (Atlas recommended)
2. **Create backend application** in Dokploy
3. **Configure environment variables**
4. **Deploy backend**
5. **Update frontend** with backend URL
6. **Test full application** functionality

## 🔧 **Troubleshooting Commands**
```bash
# Check if backend is running
curl https://your-backend-domain.com/api/test

# Check database connection
curl https://your-backend-domain.com/api/auth/test

# View logs in Dokploy dashboard
# Or SSH into server and check logs
```
