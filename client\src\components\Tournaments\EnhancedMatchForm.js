import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Divider,
  Card,
  CardMedia,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControlLabel,
  Switch
} from '@mui/material';
import {
  Save as SaveIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import { useAuth } from '../../hooks/useAuth';
import { addMatch } from '../../services/tournamentService';

/**
 * Enhanced Match Form Component
 *
 * This component provides an improved match form that better matches cricket scorecard structure
 * and allows for better handling of extracted data from scorecards.
 *
 * @param {Object} props Component props
 * @param {boolean} props.open Whether the dialog is open
 * @param {Function} props.onClose Callback when dialog is closed
 * @param {Object} props.tournament Tournament object
 * @param {Function} props.onMatchAdded Callback when match is added
 * @param {Object} props.initialData Initial data for the form (from OCR)
 * @param {string} props.capturedImage URL of the captured scorecard image
 */
const EnhancedMatchForm = ({
  open,
  onClose,
  tournament,
  onMatchAdded,
  initialData = null,
  capturedImage = null
}) => {
  const { user } = useAuth();
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [teams, setTeams] = useState([]);

  // Form data with enhanced structure
  const [formData, setFormData] = useState({
    // Match details
    team1: '',
    team2: '',
    team1Name: '', // Store OCR team names for display
    team2Name: '',
    yourTeamId: '',
    date: new Date(),
    venue: '',
    matchTime: '',

    // Match result
    team1Score: {
      runs: 0,
      wickets: 0,
      overs: 0
    },
    team2Score: {
      runs: 0,
      wickets: 0,
      overs: 0
    },
    winnerId: '',
    isTie: false,
    playerOfMatch: '',
    matchNotes: '',

    // Player statistics with detailed structure
    team1Batsmen: [],
    team1Bowlers: [],
    team2Batsmen: [],
    team2Bowlers: []
  });  // OCR validation state
  const [ocrValidation, setOcrValidation] = useState({
    hasOcrData: false,
    potentialErrors: [],
    userValidated: {}
  });

  // Initialize form when opened - moved here to comply with Rules of Hooks
  useEffect(() => {
    if (!open || !user) return;

    try {
      console.log('EnhancedMatchForm initializing...');
      console.log('Tournament data:', tournament);
      console.log('Initial data:', initialData);
      console.log('User data:', user);

      // Get all teams in the tournament with safety checks
      const allTeams = tournament?.registeredTeams || [];
      console.log('Raw teams from tournament:', allTeams);

      const validTeams = Array.isArray(allTeams) ?
        allTeams.filter(team => team && typeof team === 'object' && team._id && team.teamName) :
        [];

      setTeams(validTeams);

      console.log('EnhancedMatchForm initialized with tournament:', tournament);
      console.log('Valid teams:', validTeams);

      // Handle initial data from OCR
      if (initialData) {
        console.log('Processing initial data from OCR:', initialData);
        try {
          const mappedData = mapExtractedDataToForm(initialData, user, validTeams);
          console.log('Mapped form data:', mappedData);
          setFormData(mappedData);

          // Set OCR validation state
          const potentialErrors = detectPotentialOcrErrors(initialData);
          setOcrValidation({
            hasOcrData: true,
            potentialErrors,
            userValidated: {}
          });

          console.log('OCR validation set:', { hasOcrData: true, potentialErrors });
        } catch (error) {
          console.error('Error processing initial data:', error);
          setError('Error processing OCR data. Please enter match details manually.');
        }
      } else {
        console.log('No initial data provided');
      }
    } catch (error) {
      console.error('Error initializing form:', error);
      setError('Error initializing form. Please try again.');
    }
  }, [open, tournament, user, initialData]);

  // Ensure all player arrays are properly initialized
  if (!formData.team1Batsmen) formData.team1Batsmen = [];
  if (!formData.team1Bowlers) formData.team1Bowlers = [];
  if (!formData.team2Batsmen) formData.team2Batsmen = [];
  if (!formData.team2Bowlers) formData.team2Bowlers = [];

  // Safety check: Don't render until formData is properly initialized
  if (!formData || !formData.team1Score || !formData.team2Score) {
    return (
      <Dialog open={open} onClose={onClose} fullWidth maxWidth="md">
        <DialogTitle>Loading...</DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <Typography>Initializing form...</Typography>
          </Box>
        </DialogContent>
      </Dialog>
    );
  }

  // Handle player statistics changes
  const handlePlayerStatsChange = (team, type, index, field, value) => {
    setFormData(prev => {
      const arrayKey = `${team}${type.charAt(0).toUpperCase() + type.slice(1)}`;
      const newArray = [...prev[arrayKey]];
      
      // Validate numeric fields
      let validatedValue = value;
      if (['runs', 'balls', 'wickets'].includes(field)) {
        validatedValue = Math.max(0, parseInt(value) || 0);
      } else if (field === 'overs') {
        validatedValue = Math.max(0, parseFloat(value) || 0);
      }

      newArray[index] = {
        ...newArray[index],
        [field]: validatedValue,
        modified: true // Track manual modifications
      };

      // Update OCR validation state
      setOcrValidation(prev => ({
        ...prev,
        userValidated: {
          ...prev.userValidated,
          [`${arrayKey}.${index}.${field}`]: true
        }
      }));

      return {
        ...prev,
        [arrayKey]: newArray
      };
    });
  };

  // Get field styling based on OCR confidence and validation state
  const getFieldStyling = (ocrConfidence, hasOcrData, isValidated, isModified) => ({
    bgcolor: isModified ? 'warning.light' : 
            isValidated ? 'success.light' : 
            hasOcrData ? (ocrConfidence > 0.8 ? 'success.light' : 'warning.light') : 
            'background.paper',
    '&:hover': {
      bgcolor: 'action.hover'
    }
  });

  // Handle form input changes
  const handleChange = (event) => {
    const { name, value, type, checked } = event.target;
    
    setFormData(prev => {
      const newData = { ...prev };
      
      // Handle nested properties (e.g., "team1Score.runs")
      if (name.includes('.')) {
        const keys = name.split('.');
        let current = newData;
        
        for (let i = 0; i < keys.length - 1; i++) {
          if (!current[keys[i]]) current[keys[i]] = {};
          current = current[keys[i]];
        }
        
        current[keys[keys.length - 1]] = type === 'checkbox' ? checked : 
                                        type === 'number' ? (parseFloat(value) || 0) : value;
      } else {
        newData[name] = type === 'checkbox' ? checked : 
                       type === 'number' ? (parseFloat(value) || 0) : value;
      }
      
      return newData;
    });
  };

  // Handle form submission
  const handleSubmit = async (event) => {
    event.preventDefault();
    
    try {
      setSubmitting(true);
      setError(null);
      
      // Validate required fields
      if (!formData.team1 || !formData.team2) {
        throw new Error('Please select both teams');
      }
      
      if (!formData.venue) {
        throw new Error('Please enter a venue');
      }
      
      // Prepare match data for API
      // Sanitize ocrData.playerPerformances to replace 'team1'/'team2' with ObjectIds
      let sanitizedOcrData = initialData ? JSON.parse(JSON.stringify(initialData)) : null;
      if (sanitizedOcrData && Array.isArray(sanitizedOcrData.playerPerformances)) {
        sanitizedOcrData.playerPerformances = sanitizedOcrData.playerPerformances.map(perf => {
          if (perf.team === 'team1') return { ...perf, team: formData.team1 };
          if (perf.team === 'team2') return { ...perf, team: formData.team2 };
          return perf;
        });
      }

      // Transform data to match server API expectations
      const apiMatchData = {
        opponentTeamId: formData.team1 === user.team ? formData.team2 : formData.team1,
        isHomeMatch: formData.team1 === user.team,
        date: formData.date || new Date(),
        venue: formData.venue || 'Default Venue',
        homeTeamBattedFirst: true, // Default value
        scorecardImage: capturedImage || null,
        ocrData: sanitizedOcrData,
        result: {
          winnerId: formData.winnerId && formData.winnerId.trim() !== '' ? formData.winnerId : null,
          team1Score: formData.team1Score,
          team2Score: formData.team2Score,
          resultText: formData.matchNotes,
          playerOfMatch: formData.playerOfMatch,
          team1Batsmen: formData.team1Batsmen,
          team1Bowlers: formData.team1Bowlers,
          team2Batsmen: formData.team2Batsmen,
          team2Bowlers: formData.team2Bowlers,
          matchNotes: formData.matchNotes,
          isTie: formData.isTie || false
        }
      };

      // Submit match to API
      console.log('Submitting match data to API:', apiMatchData);
      const result = await addMatch(tournament._id, apiMatchData);

      // Notify parent component with the result
      if (onMatchAdded) {
        onMatchAdded(result);
      }
      
      setSuccess('Match added successfully!');
      
      // Close dialog after success
      setTimeout(() => {
        onClose();
      }, 1500);
      
    } catch (error) {
      console.error('Error submitting match:', error);
      setError(error.message);
    } finally {
      setSubmitting(false);    }
  };

  // Map extracted OCR data to form structure
  const mapExtractedDataToForm = (extractedData, user, allTeams) => {
    console.log('Mapping OCR data to form:', extractedData);
    console.log('User for mapping:', user);
    console.log('Teams for mapping:', allTeams);

    // Validate input data
    if (!extractedData || typeof extractedData !== 'object') {
      console.error('Invalid extracted data:', extractedData);
      throw new Error('Invalid OCR data structure');
    }

    if (!user || typeof user !== 'object') {
      console.error('Invalid user data:', user);
      throw new Error('Invalid user data');
    }

    if (!Array.isArray(allTeams)) {
      console.error('Invalid teams data:', allTeams);
      throw new Error('Invalid teams data');
    }

    // Extract match time if available
    const matchTime = extractMatchTime(extractedData);

    // Try to match OCR team names with registered teams
    const findTeamByName = (ocrTeamName) => {
      if (!ocrTeamName || !allTeams || !allTeams.length) return '';

      // First try exact match
      let matchedTeam = allTeams.find(team =>
        team && team.teamName && team.teamName.toLowerCase() === ocrTeamName.toLowerCase()
      );

      // If no exact match, try partial match
      if (!matchedTeam) {
        matchedTeam = allTeams.find(team =>
          team && team.teamName && (
            team.teamName.toLowerCase().includes(ocrTeamName.toLowerCase()) ||
            ocrTeamName.toLowerCase().includes(team.teamName.toLowerCase())
          )
        );
      }

      return matchedTeam && matchedTeam._id ? matchedTeam._id : '';
    };

    // Map team names to team IDs
    const team1Id = findTeamByName(extractedData.team1);
    const team2Id = findTeamByName(extractedData.team2);

    // Default form data with enhanced structure
    const defaultData = {
      team1: team1Id,
      team2: team2Id,
      team1Name: extractedData.team1 || '', // Store original OCR names for display
      team2Name: extractedData.team2 || '',
      yourTeamId: user.team || '',
      date: new Date(),
      venue: extractedData.venue || '',
      matchTime: matchTime,
      team1Score: {
        runs: extractedData.team1Score?.runs || 0,
        wickets: extractedData.team1Score?.wickets || 0,
        overs: extractedData.team1Score?.overs || 0
      },
      team2Score: {
        runs: extractedData.team2Score?.runs || 0,
        wickets: extractedData.team2Score?.wickets || 0,
        overs: extractedData.team2Score?.overs || 0
      },
      winnerId: '',
      isTie: false,
      playerOfMatch: extractedData.playerOfMatch || '',
      matchNotes: extractedData.resultText ? `Match result extracted from scorecard: ${extractedData.resultText}` : '',
      team1Batsmen: normalizePlayerStats(extractedData?.team1Batsmen || [], 'batting'),
      team1Bowlers: normalizePlayerStats(extractedData?.team1Bowlers || [], 'bowling'),
      team2Batsmen: normalizePlayerStats(extractedData?.team2Batsmen || [], 'batting'),
      team2Bowlers: normalizePlayerStats(extractedData?.team2Bowlers || [], 'bowling')
    };

    // Try to determine winner from result text
    if (extractedData.resultText) {
      const resultText = extractedData.resultText.toLowerCase();

      if (resultText.includes('tie') || resultText.includes('draw')) {
        defaultData.isTie = true;
      } else if (extractedData.team1 && resultText.includes(extractedData.team1.toLowerCase())) {
        // Team 1 won
        defaultData.winnerId = 'team1';
      } else if (extractedData.team2 && resultText.includes(extractedData.team2.toLowerCase())) {
        // Team 2 won
        defaultData.winnerId = 'team2';
      }
    }

    // Set up OCR validation
    const potentialErrors = detectPotentialOcrErrors(extractedData);
    setOcrValidation({
      hasOcrData: true,
      potentialErrors: potentialErrors,
      userValidated: {}
    });

    return defaultData;
  };


  // Prepare match data for API
  const prepareMatchDataForApi = () => {
    // Determine which team is home and away based on user selection
    const isTeam1Home = formData.yourTeamId === formData.team1;

    // Prepare player performances
    const playerPerformances = [];

    // Add Team 1 player performances
    formData.team1Batsmen.forEach(batsman => {
      if (batsman.name.trim()) {
        playerPerformances.push({
          playerName: batsman.name.trim(),
          team: typeof formData.team1 === 'string' && formData.team1.match(/^[0-9a-fA-F]{24}$/) ? formData.team1 : '',
          batting: {
            runs: batsman.runs || 0,
            ballsFaced: batsman.balls || 0,
            fours: 0, // Not captured in current form
            sixes: 0, // Not captured in current form
            notOut: false // Not captured in current form
          },
          bowling: {
              overs: 0,
              runs: 0,
              wickets: 0
            },
          fielding: {
            catches: 0,
            runOuts: 0,
            stumpings: 0
          }
        });
      }
    });

    formData.team1Bowlers.forEach(bowler => {
      if (bowler.name.trim()) {
        // Check if this player already exists in performances
        const existingIndex = playerPerformances.findIndex(p =>
          p.playerName === bowler.name.trim() && p.team === formData.team1
        );

        if (existingIndex >= 0) {
          // Update existing player's bowling stats
          playerPerformances[existingIndex].bowling = {
            wickets: bowler.wickets || 0,
            runs: bowler.runs || 0,
            overs: bowler.overs || 0,
            maidens: bowler.maidens || 0,
            figures: `${bowler.wickets}/${bowler.runs}`
          };
        } else {
          // Add new player with bowling stats
          playerPerformances.push({
            playerName: bowler.name.trim(),
            team: formData.team1,
            batting: {
              runs: 0,
              ballsFaced: 0,
              fours: 0,
              sixes: 0,
              notOut: false
            },
            bowling: {
              wickets: bowler.wickets || 0,
              runs: bowler.runs || 0,
              overs: bowler.overs || 0,
              maidens: bowler.maidens || 0,
              figures: `${bowler.wickets}/${bowler.runs}`
            },
            fielding: {
              catches: 0,
              runOuts: 0,
              stumpings: 0
            }
          });
        }
      }
    });

    // Add Team 2 player performances
    formData.team2Batsmen.forEach(batsman => {
      if (batsman.name.trim()) {
        playerPerformances.push({
          playerName: batsman.name.trim(),
          team: typeof formData.team2 === 'string' && formData.team2.match(/^[0-9a-fA-F]{24}$/) ? formData.team2 : '',
          batting: {
            runs: batsman.runs || 0,
            ballsFaced: batsman.balls || 0,
            fours: 0,
            sixes: 0,
            notOut: false
          },
          bowling: {
              overs: 0,
              runs: 0,
              wickets: 0
            },
          fielding: {
            catches: 0,
            runOuts: 0,
            stumpings: 0
          }
        });
      }
    });

    formData.team2Bowlers.forEach(bowler => {
      if (bowler.name.trim()) {
        // Check if this player already exists in performances
        const existingIndex = playerPerformances.findIndex(p =>
          p.playerName === bowler.name.trim() && p.team === formData.team2
        );

        if (existingIndex >= 0) {
          // Update existing player's bowling stats
          playerPerformances[existingIndex].bowling = {
            wickets: bowler.wickets || 0,
            runs: bowler.runs || 0,
            overs: bowler.overs || 0,
            maidens: bowler.maidens || 0,
            figures: `${bowler.wickets}/${bowler.runs}`
          };
        } else {
          // Add new player with bowling stats
          playerPerformances.push({
            playerName: bowler.name.trim(),
            team: formData.team2,
            batting: {
              runs: 0,
              ballsFaced: 0,
              fours: 0,
              sixes: 0,
              notOut: false
            },
            bowling: {
              wickets: bowler.wickets || 0,
              runs: bowler.runs || 0,
              overs: bowler.overs || 0,
              maidens: bowler.maidens || 0,
              figures: `${bowler.wickets}/${bowler.runs}`
            },
            fielding: {
              catches: 0,
              runOuts: 0,
              stumpings: 0
            }
          });
        }
      }
    });

    // Map form data to API structure
    return {
      homeTeam: isTeam1Home ? formData.team1 : formData.team2,
      awayTeam: isTeam1Home ? formData.team2 : formData.team1,
      date: formData.date,
      venue: formData.venue,
      matchTime: formData.matchTime,
      result: {
        homeTeamScore: isTeam1Home ? formData.team1Score : formData.team2Score,
        awayTeamScore: isTeam1Home ? formData.team2Score : formData.team1Score,
        winnerId: formData.winnerId === 'team1' ? formData.team1 :
                 formData.winnerId === 'team2' ? formData.team2 : '',
        isTie: formData.isTie,
        manOfTheMatch: formData.playerOfMatch,
        description: formData.matchNotes,
        playerPerformances: playerPerformances
      }
    };
  };

  // Helper function to extract match time from OCR data
  const extractMatchTime = (extractedData) => {
    // Look for time patterns in the raw text or specific fields
    if (extractedData.rawText) {
      const timePattern = /(\d{1,2}:\d{2}\s*(?:AM|PM|am|pm))/i;
      const match = extractedData.rawText.match(timePattern);
      if (match) {
        return match[1];
      }
    }
    return '';
  };

  // Helper function to normalize player statistics
  const normalizePlayerStats = (players, type) => {
    console.log(`Normalizing ${type} players:`, players);

    // Safety checks
    if (!players) {
      console.log(`Players is null/undefined for ${type}`);
      return [];
    }

    if (!Array.isArray(players)) {
      console.log(`Players is not an array for ${type}:`, typeof players);
      return [];
    }

    try {
      const validPlayers = players.filter(player => {
        const isValid = player && typeof player === 'object' && player.name;
        if (!isValid) {
          console.log(`Invalid player filtered out for ${type}:`, player);
        }
        return isValid;
      });

      const normalizedPlayers = validPlayers.map((player, index) => {
        // Extra safety checks for player object
        if (!player || typeof player !== 'object') {
          console.warn(`Invalid player object at index ${index}:`, player);
          return {
            id: `${type}_${index}`,
            name: '',
            runs: 0,
            balls: 0,
            wickets: 0,
            overs: 0,
            maidens: 0,
            ocrConfidence: 'low'
          };
        }

        const normalized = {
          id: `${type}_${index}`,
          name: String(player.name || ''),
          runs: type === 'batting' ? (Number(player.runs) || 0) : (Number(player.runs) || 0),
          balls: type === 'batting' ? (Number(player.balls) || 0) : 0,
          wickets: type === 'bowling' ? (Number(player.wickets) || 0) : 0,
          overs: type === 'bowling' ? (Number(player.overs) || 0) : 0,
          maidens: type === 'bowling' ? (Number(player.maidens) || 0) : 0,
          ocrConfidence: player.ocrConfidence || 'medium' // Include OCR confidence from backend
        };
        console.log(`Normalized ${type} player:`, normalized);
        return normalized;
      });

      console.log(`Final normalized ${type} players:`, normalizedPlayers);
      return normalizedPlayers;
    } catch (error) {
      console.error(`Error normalizing ${type} players:`, error);
      return [];
    }
  };

  // Helper function to detect potential OCR errors
  const detectPotentialOcrErrors = (extractedData) => {
    const errors = [];

    // Check for suspicious team names (too short, contains numbers, etc.)
    if (extractedData.team1 && extractedData.team1.length < 3) {
      errors.push({ field: 'team1', reason: 'Team name seems too short' });
    }
    if (extractedData.team2 && extractedData.team2.length < 3) {
      errors.push({ field: 'team2', reason: 'Team name seems too short' });
    }

    // Check for unrealistic scores
    if (extractedData.team1Score?.runs > 500) {
      errors.push({ field: 'team1Score.runs', reason: 'Runs seem unusually high' });
    }
    if (extractedData.team2Score?.runs > 500) {
      errors.push({ field: 'team2Score.runs', reason: 'Runs seem unusually high' });
    }

    // Check for missing venue
    if (!extractedData.venue || extractedData.venue.length < 3) {
      errors.push({ field: 'venue', reason: 'Venue information may be incomplete' });
    }

    // Check for player names that seem incomplete or have low OCR confidence
    const allPlayers = [
      ...(extractedData.team1Batsmen || []),
      ...(extractedData.team1Bowlers || []),
      ...(extractedData.team2Batsmen || []),
      ...(extractedData.team2Bowlers || [])
    ];

    allPlayers.forEach((player, index) => {
      if (player.name && player.name.length < 3) {
        errors.push({
          field: `player_${index}`,
          reason: `Player name "${player.name}" seems incomplete`
        });
      }
      if (player.ocrConfidence === 'low') {
        errors.push({
          field: `player_${index}`,
          reason: `Player data "${player.name}" has low OCR confidence - please verify`        });
      }
    });
    
    return errors;
  };

  // Function to mark field as user-validated
  const markFieldValidated = (arrayKey, playerIndex, field) => {
    // Mark field as user-validated
    setOcrValidation(prev => ({
      ...prev,
      userValidated: {
        ...prev.userValidated,
        [`${arrayKey}.${playerIndex}.${field}`]: true
      }
    }));
  };

  // Add/remove player functions
  const addPlayer = (team, type) => {
    const arrayKey = `${team}${type.charAt(0).toUpperCase() + type.slice(1)}`;
    const newPlayer = {
      id: `${type}_${Date.now()}`,
      name: '',
      runs: 0,
      balls: 0,
      wickets: 0,
      overs: 0,
      maidens: 0,
      economy: 0
    };

    setFormData(prev => ({
      ...prev,
      [arrayKey]: [...prev[arrayKey], newPlayer]
    }));
  };

  const removePlayer = (team, type, playerIndex) => {
    const arrayKey = `${team}${type.charAt(0).toUpperCase() + type.slice(1)}`;

    setFormData(prev => ({
      ...prev,
      [arrayKey]: prev[arrayKey].filter((_, index) => index !== playerIndex)
    }));
  };
  // Calculate match result based on scores
  const calculateMatchResult = () => {
    if (formData.isTie) {
      return 'Match declared as tie/draw';
    }
    
    const team1Runs = formData.team1Score.runs || 0;
    const team2Runs = formData.team2Score.runs || 0;
    const team1Wickets = formData.team1Score.wickets || 0;
    const team2Wickets = formData.team2Score.wickets || 0;
    const team1Name = formData.team1Name || 'Team 1';
    const team2Name = formData.team2Name || 'Team 2';

    if (team1Runs === 0 && team2Runs === 0) {
      return 'Enter team scores to see match result';
    }
    
    if (team1Runs > team2Runs) {
      const margin = team1Runs - team2Runs;
      return `${team1Name} won by ${margin} runs`;
    } else if (team2Runs > team1Runs) {
      const wicketsLeft = 10 - team2Wickets;
      return `${team2Name} won by ${wicketsLeft} wickets`;
    }
      if (team1Runs === team2Runs && team1Runs > 0) {
      return 'Match tied - same score';    
    }
    return 'Enter team scores to see match result';
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="md"
    >
      <DialogTitle>
        Add Match Result
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{ position: 'absolute', right: 8, top: 8 }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent dividers>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ mb: 2 }}>
            {success}
          </Alert>
        )}

        {/* Scorecard Image (if available) */}
        {capturedImage && (
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle1" gutterBottom>
              Uploaded Scorecard
            </Typography>
            <Card>
              <CardMedia
                component="img"
                image={capturedImage}
                alt="Scorecard"
                sx={{ maxHeight: 300, objectFit: 'contain' }}
              />
            </Card>
          </Box>
        )}

        {/* Extracted Data Display */}
        {initialData && (
          <Box sx={{ mb: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1, border: '1px solid', borderColor: 'divider' }}>
            <Typography variant="subtitle1" gutterBottom>
              Extracted Data from Scorecard
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2">
                  <strong>Team 1:</strong> {initialData.team1}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2">
                  <strong>Team 2:</strong> {initialData.team2}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2">
                  <strong>Team 1 Score:</strong> {initialData.team1Score?.runs}-{initialData.team1Score?.wickets} ({initialData.team1Score?.overs} overs)
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2">
                  <strong>Team 2 Score:</strong> {initialData.team2Score?.runs}-{initialData.team2Score?.wickets} ({initialData.team2Score?.overs} overs)
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="body2">
                  <strong>Result:</strong> {initialData.resultText}
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="body2">
                  <strong>Player of the Match:</strong> {initialData.playerOfMatch}
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="body2">
                  <strong>Venue:</strong> {initialData.venue}
                </Typography>
              </Grid>
            </Grid>
          </Box>
        )}

        {/* OCR Validation Warnings */}
        {ocrValidation.hasOcrData && ocrValidation.potentialErrors.length > 0 && (
          <Box sx={{ mb: 3 }}>
            <Alert severity="warning" sx={{ mb: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                Please verify the following fields - OCR may have made errors:
              </Typography>
              <ul style={{ margin: 0, paddingLeft: '20px' }}>
                {ocrValidation.potentialErrors.map((error, index) => (
                  <li key={index}>
                    <strong>{error.field}:</strong> {error.reason}
                  </li>
                ))}
              </ul>
              <Typography variant="body2" sx={{ mt: 1, fontStyle: 'italic' }}>
                All fields below are editable. Please review and correct any inaccuracies.
              </Typography>
            </Alert>
          </Box>
        )}

        {/* Match Form */}
        <form>
          <Grid container spacing={3}>
            {/* Match Details Section */}
            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>
                Match Details
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>

            {/* Team Selection */}
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required>
                <InputLabel>Team 1</InputLabel>
                <Select
                  name="team1"
                  value={formData.team1}
                  onChange={handleChange}
                  disabled={submitting}
                >
                  {formData.team1Name && (
                    <MenuItem value="" disabled>
                      OCR Detected: {formData.team1Name}
                    </MenuItem>
                  )}
                  {teams && teams.length > 0 ? (
                    teams.filter(team => team && team._id && team.teamName).map(team => (
                      <MenuItem key={team._id} value={team._id}>
                        {team.teamName}
                        {formData.team1Name && team.teamName && team.teamName.toLowerCase().includes(formData.team1Name.toLowerCase()) && ' (Matched)'}
                      </MenuItem>
                    ))
                  ) : (
                    <MenuItem value="" disabled>
                      No teams available
                    </MenuItem>
                  )}
                </Select>
              </FormControl>
              {formData.team1Name && (
                <Typography variant="caption" color="primary" sx={{ mt: 1, display: 'block' }}>
                  OCR detected: "{formData.team1Name}"
                </Typography>
              )}
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required>
                <InputLabel>Team 2</InputLabel>
                <Select
                  name="team2"
                  value={formData.team2}
                  onChange={handleChange}
                  disabled={submitting}
                >
                  {formData.team2Name && (
                    <MenuItem value="" disabled>
                      OCR Detected: {formData.team2Name}
                    </MenuItem>
                  )}
                  {teams && teams.length > 0 ? (
                    teams.filter(team => team && team._id && team.teamName).map(team => (
                      <MenuItem key={team._id} value={team._id}>
                        {team.teamName}
                        {formData.team2Name && team.teamName && team.teamName.toLowerCase().includes(formData.team2Name.toLowerCase()) && ' (Matched)'}
                      </MenuItem>
                    ))
                  ) : (
                    <MenuItem value="" disabled>
                      No teams available
                    </MenuItem>
                  )}
                </Select>
              </FormControl>
              {formData.team2Name && (
                <Typography variant="caption" color="primary" sx={{ mt: 1, display: 'block' }}>
                  OCR detected: "{formData.team2Name}"
                </Typography>
              )}
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required>
                <InputLabel>Your Team</InputLabel>
                <Select
                  name="yourTeamId"
                  value={formData.yourTeamId}
                  onChange={handleChange}
                  disabled={submitting}
                >
                  {formData.team1 && (
                    <MenuItem value={formData.team1}>
                      {teams && teams.find(t => t && t._id === formData.team1)?.teamName || 'Team 1'}
                    </MenuItem>
                  )}
                  {formData.team2 && (
                    <MenuItem value={formData.team2}>
                      {teams && teams.find(t => t && t._id === formData.team2)?.teamName || 'Team 2'}
                    </MenuItem>
                  )}
                  {!formData.team1 && !formData.team2 && (
                    <MenuItem value="" disabled>
                      Select Team 1 and Team 2 first
                    </MenuItem>
                  )}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Match Date"
                type="date"
                name="date"
                value={formData.date instanceof Date ? formData.date.toISOString().split('T')[0] : formData.date}
                onChange={(e) => handleChange({ target: { name: 'date', value: e.target.value } })}
                InputLabelProps={{ shrink: true }}
                disabled={submitting}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Venue"
                name="venue"
                value={formData.venue}
                onChange={handleChange}
                disabled={submitting}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Match Time"
                name="matchTime"
                value={formData.matchTime}
                onChange={handleChange}
                placeholder="e.g., 9:17 PM"
                disabled={submitting}
              />
            </Grid>

            {/* Match Result Section */}
            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>
                Match Result
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>

            {/* Team 1 Score */}
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" gutterBottom sx={{
                fontWeight: 'bold',
                bgcolor: 'primary.light',
                color: 'white',
                p: 1,
                borderRadius: 1,
                textAlign: 'center'
              }}>
                {formData.team1Name || 'Team 1'} Score
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={4}>
                  <TextField
                    fullWidth
                    label="Runs"
                    name="team1Score.runs"
                    type="number"
                    value={formData.team1Score.runs}
                    onChange={handleChange}
                    inputProps={{ min: 0 }}
                    disabled={submitting}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        bgcolor: ocrValidation.hasOcrData ? 'success.light' : 'background.paper'
                      }
                    }}
                  />
                </Grid>
                <Grid item xs={4}>
                  <TextField
                    fullWidth
                    label="Wickets"
                    name="team1Score.wickets"
                    type="number"
                    value={formData.team1Score.wickets}
                    onChange={handleChange}
                    inputProps={{ min: 0, max: 10 }}
                    disabled={submitting}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        bgcolor: ocrValidation.hasOcrData ? 'success.light' : 'background.paper'
                      }
                    }}
                  />
                </Grid>
                <Grid item xs={4}>
                  <TextField
                    fullWidth
                    label="Overs"
                    name="team1Score.overs"
                    type="number"
                    value={formData.team1Score.overs}
                    onChange={handleChange}
                    inputProps={{ min: 0, step: 0.1 }}
                    disabled={submitting}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        bgcolor: ocrValidation.hasOcrData ? 'success.light' : 'background.paper'
                      }
                    }}
                  />
                </Grid>
              </Grid>
            </Grid>

            {/* Team 2 Score */}
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" gutterBottom sx={{
                fontWeight: 'bold',
                bgcolor: 'primary.light',
                color: 'white',
                p: 1,
                borderRadius: 1,
                textAlign: 'center'
              }}>
                {formData.team2Name || 'Team 2'} Score
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={4}>
                  <TextField
                    fullWidth
                    label="Runs"
                    name="team2Score.runs"
                    type="number"
                    value={formData.team2Score.runs}
                    onChange={handleChange}
                    inputProps={{ min: 0 }}
                    disabled={submitting}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        bgcolor: ocrValidation.hasOcrData ? 'success.light' : 'background.paper'
                      }
                    }}
                  />
                </Grid>
                <Grid item xs={4}>
                  <TextField
                    fullWidth
                    label="Wickets"
                    name="team2Score.wickets"
                    type="number"
                    value={formData.team2Score.wickets}
                    onChange={handleChange}
                    inputProps={{ min: 0, max: 10 }}
                    disabled={submitting}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        bgcolor: ocrValidation.hasOcrData ? 'success.light' : 'background.paper'
                      }
                    }}
                  />
                </Grid>
                <Grid item xs={4}>
                  <TextField
                    fullWidth
                    label="Overs"
                    name="team2Score.overs"
                    type="number"
                    value={formData.team2Score.overs}
                    onChange={handleChange}
                    inputProps={{ min: 0, step: 0.1 }}
                    disabled={submitting}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        bgcolor: ocrValidation.hasOcrData ? 'success.light' : 'background.paper'
                      }
                    }}
                  />
                </Grid>
              </Grid>
            </Grid>

            {/* Auto-calculated Match Result */}
            <Grid item xs={12}>
              <Box sx={{
                p: 2,
                bgcolor: 'info.light',
                borderRadius: 1,
                border: '1px solid',
                borderColor: 'info.main'
              }}>
                <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 'bold' }}>
                  Match Result (Auto-calculated)
                </Typography>
                <Typography variant="body1" sx={{ color: 'info.dark' }}>
                  {calculateMatchResult()}
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.isTie}
                    onChange={(e) => setFormData(prev => ({ ...prev, isTie: e.target.checked, winnerId: e.target.checked ? '' : prev.winnerId }))}
                    name="isTie"
                    disabled={submitting}
                  />
                }
                label="Override: Match was a tie/draw"
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Player of the Match"
                name="playerOfMatch"
                value={formData.playerOfMatch}
                onChange={handleChange}
                disabled={submitting}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Match Notes"
                name="matchNotes"
                value={formData.matchNotes}
                onChange={handleChange}
                multiline
                rows={2}
                disabled={submitting}
              />
            </Grid>

            {/* Player Statistics Section */}
            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom sx={{ mt: 3 }}>
                Player Statistics
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>

            {/* Team 1 Player Statistics */}
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" gutterBottom sx={{
                bgcolor: 'success.light',
                color: 'white',
                p: 1,
                borderRadius: 1,
                textAlign: 'center',
                fontWeight: 'bold'
              }}>
                {formData.team1Name || 'Team 1'} Batsmen
              </Typography>
              <Box sx={{ border: '1px solid', borderColor: 'success.main', borderRadius: 1, p: 2, mb: 2, bgcolor: 'success.light' }}>
                {formData.team1Batsmen && formData.team1Batsmen.length > 0 ? (
                  formData.team1Batsmen.filter(batsman => batsman && typeof batsman === 'object').map((batsman, index) => (
                    <Grid container spacing={2} key={batsman.id || index} sx={{ mb: 2 }}>
                      <Grid item xs={12} sm={4}>
                        <TextField
                          fullWidth
                          label="Player Name"
                          value={batsman.name || ''}
                          onChange={(e) => handlePlayerStatsChange('team1', 'batsmen', index, 'name', e.target.value)}
                          size="small"
                          disabled={false}
                          sx={{
                            '& .MuiOutlinedInput-root': getFieldStyling(
                              batsman.ocrConfidence,
                              ocrValidation.hasOcrData,
                              ocrValidation.userValidated[`team1Batsmen.${index}.name`],
                              batsman.modified
                            )
                          }}
                        />
                      </Grid>
                      <Grid item xs={6} sm={3}>
                        <TextField
                          fullWidth
                          label="Runs"
                          type="number"
                          value={batsman.runs || 0}
                          onChange={(e) => handlePlayerStatsChange('team1', 'batsmen', index, 'runs', e.target.value)}
                          size="small"
                          inputProps={{ min: 0 }}
                          disabled={false}
                          sx={{
                            '& .MuiOutlinedInput-root': getFieldStyling(
                              batsman.ocrConfidence,
                              ocrValidation.hasOcrData,
                              ocrValidation.userValidated[`team1Batsmen.${index}.runs`],
                              batsman.modified
                            )
                          }}
                        />
                      </Grid>
                      <Grid item xs={6} sm={3}>
                        <TextField
                          fullWidth
                          label="Balls"
                          type="number"
                          value={batsman.balls || 0}
                          onChange={(e) => handlePlayerStatsChange('team1', 'batsmen', index, 'balls', e.target.value)}
                          size="small"
                          inputProps={{ min: 0 }}
                          disabled={false}
                          sx={{
                            '& .MuiOutlinedInput-root': getFieldStyling(
                              batsman.ocrConfidence,
                              ocrValidation.hasOcrData,
                              ocrValidation.userValidated[`team1Batsmen.${index}.balls`],
                              batsman.modified
                            )
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={2}>
                        <Button
                          variant="outlined"
                          color="error"
                          size="small"
                          onClick={() => removePlayer('team1', 'batsmen', index)}
                          disabled={submitting}
                          fullWidth
                        >
                          Remove
                        </Button>
                      </Grid>
                    </Grid>
                  ))
                ) : (
                  <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 2 }}>
                    No batsmen data available. Click "Add Batsman" to add manually.
                  </Typography>
                )}
                <Button
                  variant="outlined"
                  onClick={() => addPlayer('team1', 'batsmen')}
                  disabled={submitting}
                  size="small"
                  sx={{ mt: 1 }}
                >
                  Add Batsman
                </Button>
              </Box>

              <Typography variant="subtitle2" gutterBottom sx={{
                bgcolor: 'success.light',
                color: 'white',
                p: 1,
                borderRadius: 1,
                textAlign: 'center',
                fontWeight: 'bold'
              }}>
                {formData.team1Name || 'Team 1'} Bowlers
              </Typography>
              <Box sx={{ border: '1px solid', borderColor: 'success.main', borderRadius: 1, p: 2, bgcolor: 'success.light' }}>
                {formData.team1Bowlers && formData.team1Bowlers.length > 0 ? (
                  formData.team1Bowlers.filter(bowler => bowler && typeof bowler === 'object').map((bowler, index) => (
                    <Grid container spacing={2} key={bowler.id || index} sx={{ mb: 2 }}>
                      <Grid item xs={12} sm={4}>
                        <TextField
                          fullWidth
                          label="Player Name"
                          value={bowler.name || ''}
                          onChange={(e) => handlePlayerStatsChange('team1', 'bowlers', index, 'name', e.target.value)}
                          size="small"
                          disabled={false}
                          sx={{
                            '& .MuiOutlinedInput-root': getFieldStyling(
                              bowler.ocrConfidence,
                              ocrValidation.hasOcrData,
                              ocrValidation.userValidated[`team1Bowlers.${index}.name`],
                              bowler.modified
                            )
                          }}
                        />
                      </Grid>
                      <Grid item xs={6} sm={3}>
                        <TextField
                          fullWidth
                          label="Wickets"
                          type="number"
                          value={bowler.wickets || 0}
                          onChange={(e) => handlePlayerStatsChange('team1', 'bowlers', index, 'wickets', e.target.value)}
                          size="small"
                          inputProps={{ min: 0 }}
                          disabled={false}
                          sx={{
                            '& .MuiOutlinedInput-root': getFieldStyling(
                              bowler.ocrConfidence,
                              ocrValidation.hasOcrData,
                              ocrValidation.userValidated[`team1Bowlers.${index}.wickets`],
                              bowler.modified
                            )
                          }}
                        />
                      </Grid>
                      <Grid item xs={6} sm={3}>
                        <TextField
                          fullWidth
                          label="Runs"
                          type="number"
                          value={bowler.runs || 0}
                          onChange={(e) => handlePlayerStatsChange('team1', 'bowlers', index, 'runs', e.target.value)}
                          size="small"
                          inputProps={{ min: 0 }}
                          disabled={submitting}
                          sx={{
                            '& .MuiOutlinedInput-root': getFieldStyling(
                              bowler.ocrConfidence,
                              ocrValidation.hasOcrData,
                              ocrValidation.userValidated[`team1Bowlers.${index}.runs`],
                              bowler.modified
                            )
                          }}
                        />
                      </Grid>
                      <Grid item xs={6} sm={2}>
                        <Button
                          variant="outlined"
                          color="error"
                          size="small"
                          onClick={() => removePlayer('team1', 'bowlers', index)}
                          disabled={submitting}
                          fullWidth
                        >
                          Remove
                        </Button>
                      </Grid>
                    </Grid>
                  ))
                ) : (
                  <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 2 }}>
                    No bowlers data available. Click "Add Bowler" to add manually.
                  </Typography>
                )}
                <Button
                  variant="outlined"
                  onClick={() => addPlayer('team1', 'bowlers')}
                  disabled={submitting}
                  size="small"
                  sx={{ mt: 1 }}
                >
                  Add Bowler
                </Button>
              </Box>
            </Grid>

            {/* Team 2 Player Statistics */}
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" gutterBottom sx={{
                bgcolor: 'success.light',
                color: 'white',
                p: 1,
                borderRadius: 1,
                textAlign: 'center',
                fontWeight: 'bold'
              }}>
                {formData.team2Name || 'Team 2'} Batsmen
              </Typography>
              <Box sx={{ border: '1px solid', borderColor: 'success.main', borderRadius: 1, p: 2, mb: 2, bgcolor: 'success.light' }}>
                {formData.team2Batsmen && formData.team2Batsmen.length > 0 ? (
                  formData.team2Batsmen.filter(batsman => batsman && typeof batsman === 'object').map((batsman, index) => (
                    <Grid container spacing={2} key={batsman.id || index} sx={{ mb: 2 }}>
                      <Grid item xs={12} sm={4}>
                        <TextField
                          fullWidth
                          label="Player Name"
                          value={batsman.name || ''}
                          onChange={(e) => handlePlayerStatsChange('team2', 'batsmen', index, 'name', e.target.value)}
                          size="small"
                          disabled={false}
                          sx={{
                            '& .MuiOutlinedInput-root': getFieldStyling(
                              batsman.ocrConfidence,
                              ocrValidation.hasOcrData,
                              ocrValidation.userValidated[`team2Batsmen.${index}.name`],
                              batsman.modified
                            )
                          }}
                        />
                      </Grid>
                      <Grid item xs={6} sm={3}>
                        <TextField
                          fullWidth
                          label="Runs"
                          type="number"
                          value={batsman.runs || 0}
                          onChange={(e) => handlePlayerStatsChange('team2', 'batsmen', index, 'runs', e.target.value)}
                          size="small"
                          inputProps={{ min: 0 }}
                          disabled={false}
                          sx={{
                            '& .MuiOutlinedInput-root': getFieldStyling(
                              batsman.ocrConfidence,
                              ocrValidation.hasOcrData,
                              ocrValidation.userValidated[`team2Batsmen.${index}.runs`],
                              batsman.modified
                            )
                          }}
                        />
                      </Grid>
                      <Grid item xs={6} sm={3}>
                        <TextField
                          fullWidth
                          label="Balls"
                          type="number"
                          value={batsman.balls || 0}
                          onChange={(e) => handlePlayerStatsChange('team2', 'batsmen', index, 'balls', e.target.value)}
                          size="small"
                          inputProps={{ min: 0 }}
                          disabled={submitting}
                          sx={{
                            '& .MuiOutlinedInput-root': getFieldStyling(
                              batsman.ocrConfidence,
                              ocrValidation.hasOcrData,
                              ocrValidation.userValidated[`team2Batsmen.${index}.balls`],
                              batsman.modified
                            )
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={2}>
                        <Button
                          variant="outlined"
                          color="error"
                          size="small"
                          onClick={() => removePlayer('team2', 'batsmen', index)}
                          disabled={submitting}
                          fullWidth
                        >
                          Remove
                        </Button>
                      </Grid>
                    </Grid>
                  ))
                ) : (
                  <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 2 }}>
                    No batsmen data available. Click "Add Batsman" to add manually.
                  </Typography>
                )}
                <Button
                  variant="outlined"
                  onClick={() => addPlayer('team2', 'batsmen')}
                  disabled={submitting}
                  size="small"
                  sx={{ mt: 1 }}
                >
                  Add Batsman
                </Button>
              </Box>

              <Typography variant="subtitle2" gutterBottom sx={{
                bgcolor: 'success.light',
                color: 'white',
                p: 1,
                borderRadius: 1,
                textAlign: 'center',
                fontWeight: 'bold'
              }}>
                {formData.team2Name || 'Team 2'} Bowlers
              </Typography>
              <Box sx={{ border: '1px solid', borderColor: 'success.main', borderRadius: 1, p: 2, bgcolor: 'success.light' }}>
                {formData.team2Bowlers && formData.team2Bowlers.length > 0 ? (
                  formData.team2Bowlers.filter(bowler => bowler && typeof bowler === 'object').map((bowler, index) => (
                    <Grid container spacing={2} key={bowler.id || index} sx={{ mb: 2 }}>
                      <Grid item xs={12} sm={4}>
                        <TextField
                          fullWidth
                          label="Player Name"
                          value={bowler.name || ''}
                          onChange={(e) => handlePlayerStatsChange('team2', 'bowlers', index, 'name', e.target.value)}
                          size="small"
                          disabled={false}
                          sx={{
                            '& .MuiOutlinedInput-root': getFieldStyling(
                              bowler.ocrConfidence,
                              ocrValidation.hasOcrData,
                              ocrValidation.userValidated[`team2Bowlers.${index}.name`],
                              bowler.modified
                            )
                          }}
                        />
                      </Grid>
                      <Grid item xs={6} sm={3}>
                        <TextField
                          fullWidth
                          label="Wickets"
                          type="number"
                          value={bowler.wickets || 0}
                          onChange={(e) => handlePlayerStatsChange('team2', 'bowlers', index, 'wickets', e.target.value)}
                          size="small"
                          inputProps={{ min: 0 }}
                          disabled={false}
                          sx={{
                            '& .MuiOutlinedInput-root': getFieldStyling(
                              bowler.ocrConfidence,
                              ocrValidation.hasOcrData,
                              ocrValidation.userValidated[`team2Bowlers.${index}.wickets`],
                              bowler.modified
                            )
                          }}
                        />
                      </Grid>
                      <Grid item xs={6} sm={3}>
                        <TextField
                          fullWidth
                          label="Runs"
                          type="number"
                          value={bowler.runs || 0}
                          onChange={(e) => handlePlayerStatsChange('team2', 'bowlers', index, 'runs', e.target.value)}
                          size="small"
                          inputProps={{ min: 0 }}
                          disabled={submitting}
                          sx={{                            '& .MuiOutlinedInput-root': getFieldStyling(
                              bowler.ocrConfidence,
                              ocrValidation.hasOcrData,
                              ocrValidation.userValidated[`team2Bowlers.${index}.runs`],
                              bowler.modified
                            )
                          }}
                        />
                      </Grid>
                      <Grid item xs={6} sm={2}>
                        <Button
                          variant="outlined"
                          color="error"
                          size="small"
                          onClick={() => removePlayer('team2', 'bowlers', index)}
                          disabled={submitting}
                          fullWidth
                        >
                          Remove
                        </Button>
                      </Grid>
                    </Grid>
                  ))
                ) : (
                  <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 2 }}>
                    No bowlers data available. Click "Add Bowler" to add manually.
                  </Typography>
                )}
                <Button
                  variant="outlined"
                  onClick={() => addPlayer('team2', 'bowlers')}
                  disabled={submitting}
                  size="small"
                  sx={{ mt: 1 }}
                >
                  Add Bowler
                </Button>
              </Box>
            </Grid>
          </Grid>
        </form>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} disabled={submitting}>
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          color="primary"
          disabled={submitting || !formData.team1 || !formData.team2}
          startIcon={submitting ? <CircularProgress size={24} /> : <SaveIcon />}
        >
          {submitting ? 'Saving...' : 'Save Match'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default EnhancedMatchForm;
