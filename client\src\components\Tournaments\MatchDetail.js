import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Grid,
  Divider,
  Chip,
  Avatar,
  IconButton,
  Paper,
  Alert,
  CircularProgress,
  Tooltip,
  Card,
  CardMedia,
  CardContent,
  CardActions,
  TextField,
  Menu,
  MenuItem,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import {
  Close as CloseIcon,
  CloudUpload as UploadIcon,
  CheckCircle as VerifyIcon,
  ExpandMore as ExpandMoreIcon,
  Warning as DisputeIcon,
  Flag as FlagIcon,
  Image as ImageIcon,
  SportsCricket as CricketIcon,
  CameraAlt as CameraIcon,
  ArrowDropDown as ArrowDropDownIcon
} from '@mui/icons-material';
import { format } from 'date-fns';
import { useAuth } from '../../hooks/useAuth';
import { verifyMatch, disputeMatch } from '../../services/scorecardService';

// Helper function to construct full image URLs
const getFullImageUrl = (url) => {
  if (!url) return null;

  // If it's already a full URL (starts with http), return as is
  if (url.startsWith('http')) {
    return url;
  }

  // If it's a relative path starting with /uploads, prepend the API base URL
  if (url.startsWith('/uploads')) {
    const apiBaseUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';
    // Remove /api from the end if present, since uploads are served from root
    const baseUrl = apiBaseUrl.replace('/api', '');
    return `${baseUrl}${url}`;
  }

  // For other relative paths, return as is
  return url;
};
import ScorecardUpload from './ScorecardUpload';
import { useNavigate } from 'react-router-dom';

const MatchDetail = ({ open, onClose, match, tournament, onActionComplete }) => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [disputeDialogOpen, setDisputeDialogOpen] = useState(false);
  const [disputeReason, setDisputeReason] = useState('');
  const [selectedImage, setSelectedImage] = useState(null);
  const [imageDialogOpen, setImageDialogOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [uploadMenuAnchor, setUploadMenuAnchor] = useState(null);

  if (!match) return null;

  // Format date
  const formatDate = (date) => {
    if (!date) return 'TBD';
    return format(new Date(date), 'MMM dd, yyyy h:mm a');
  };

  // Check if user is admin
  const isAdmin = user && user.role === 'admin';

  // Check if user is team owner of one of the teams
  const isTeamOwner = user && user.role === 'team_owner' && user.team &&
    (match.homeTeam._id === user.team || match.awayTeam._id === user.team);

  // Handle upload menu open
  const handleUploadMenuOpen = (event) => {
    setUploadMenuAnchor(event.currentTarget);
  };

  // Handle upload menu close
  const handleUploadMenuClose = () => {
    setUploadMenuAnchor(null);
  };

  // Handle camera capture
  const handleCameraCapture = () => {
    handleUploadMenuClose();
    // Store match and tournament info in session storage
    sessionStorage.setItem('captureContext', JSON.stringify({
      tournamentId: tournament._id,
      matchId: match._id,
      returnPath: `/tournaments/${tournament._id}`
    }));
    // Navigate to camera capture
    navigate('/scorecard-capture');
  };

  // Handle traditional upload
  const handleTraditionalUpload = () => {
    handleUploadMenuClose();
    setUploadDialogOpen(true);
  };

  // Handle scorecard upload success
  const handleUploadSuccess = (result) => {
    setSuccess('Scorecard uploaded successfully');
    if (onActionComplete) {
      onActionComplete();
    }
  };

  // Handle verify match
  const handleVerifyMatch = async () => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);

      await verifyMatch(tournament._id, match._id);

      setSuccess('Match verified successfully');

      if (onActionComplete) {
        onActionComplete();
      }
    } catch (err) {
      console.error('Error verifying match:', err);
      setError(err.message || 'Failed to verify match. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle dispute match
  const handleDisputeMatch = async () => {
    if (!disputeReason.trim()) {
      setError('Please provide a reason for the dispute');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      setSuccess(null);

      await disputeMatch(tournament._id, match._id, disputeReason);

      setSuccess('Match disputed successfully');
      setDisputeDialogOpen(false);

      if (onActionComplete) {
        onActionComplete();
      }
    } catch (err) {
      console.error('Error disputing match:', err);
      setError(err.message || 'Failed to dispute match. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'in_progress':
        return 'warning';
      case 'cancelled':
        return 'error';
      default:
        return 'default';
    }
  };

  // Get verification status color
  const getVerificationStatusColor = (status) => {
    switch (status) {
      case 'verified':
        return 'success';
      case 'disputed':
        return 'error';
      case 'resolved':
        return 'info';
      default:
        return 'warning';
    }
  };

  // Get verification status label
  const getVerificationStatusLabel = (status) => {
    switch (status) {
      case 'verified':
        return 'Verified';
      case 'disputed':
        return 'Disputed';
      case 'resolved':
        return 'Resolved';
      default:
        return 'Pending Verification';
    }
  };

  return (
    <>
      <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <CricketIcon sx={{ mr: 1 }} />
              <Typography variant="h6">Match Details</Typography>
            </Box>
            <IconButton onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>

        <DialogContent sx={{ p: 0 }}>
          {error && (
            <Alert severity="error" sx={{ m: 2 }}>
              {error}
            </Alert>
          )}

          {success && (
            <Alert severity="success" sx={{ m: 2 }}>
              {success}
            </Alert>
          )}

          {/* Header: Match Date, Status, Format, Venue */}
          <Paper sx={{ p: 2, m: 2, borderRadius: 2, bgcolor: 'primary.dark', color: 'white' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', flexWrap: 'wrap', gap: 2 }}>
              <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'white' }}>
                {formatDate(match.date)}
              </Typography>
              <Typography variant="h6" sx={{ color: 'primary.light' }}>|</Typography>
              <Chip
                label={match.status}
                color={getStatusColor(match.status)}
                size="medium"
                sx={{ bgcolor: 'white', color: 'primary.main', fontWeight: 'bold' }}
              />
              <Typography variant="h6" sx={{ color: 'primary.light' }}>|</Typography>
              <Typography variant="h6" sx={{ color: 'white' }}>
                Format: {match.format}
              </Typography>
              <Typography variant="h6" sx={{ color: 'primary.light' }}>|</Typography>
              <Typography variant="h6" sx={{ color: 'white' }}>
                {match.venue || 'Venue not specified'}
              </Typography>
              {match.result?.verificationStatus && (
                <>
                  <Typography variant="h6" sx={{ color: 'primary.light' }}>|</Typography>
                  <Chip
                    label={getVerificationStatusLabel(match.result.verificationStatus)}
                    color={getVerificationStatusColor(match.result.verificationStatus)}
                    size="medium"
                    sx={{ bgcolor: 'white', fontWeight: 'bold' }}
                  />
                </>
              )}
              {match.result?.isTie && (
                <>
                  <Typography variant="h6" sx={{ color: 'primary.light' }}>|</Typography>
                  <Chip label="Match Tied" size="medium" color="info" sx={{ bgcolor: 'white', color: 'info.main', fontWeight: 'bold' }} />
                </>
              )}
            </Box>
          </Paper>

          {/* Scorecard Image Section - Large Central Display */}
          {(() => {
            // Get scorecard images - check multiple possible locations
            let scorecardImages = [];

            // 1. Check for images in result.scorecardImages
            if (match.result?.scorecardImages && Array.isArray(match.result.scorecardImages)) {
              scorecardImages = match.result.scorecardImages.map(img => ({
                ...img,
                url: getFullImageUrl(img.url)
              }));
            }

            // 2. If no images in result, check match.scorecardImages
            if (scorecardImages.length === 0 && match.scorecardImages && Array.isArray(match.scorecardImages)) {
              scorecardImages = match.scorecardImages.map(img => ({
                ...img,
                url: getFullImageUrl(img.url)
              }));
            }

            // 3. Check if there's a scorecard URL in the OCR data
            if (scorecardImages.length === 0 && match.ocrData?.scorecardUrl) {
              scorecardImages = [{
                url: getFullImageUrl(match.ocrData.scorecardUrl),
                uploadedBy: match.createdBy || 'system',
                uploadedAt: match.createdAt || new Date(),
                isVerified: false
              }];
            }

            // 4. Check for uploaded scorecard during match creation (EnhancedMatchForm)
            if (scorecardImages.length === 0 && match.scorecardImage) {
              scorecardImages = [{
                url: getFullImageUrl(match.scorecardImage),
                uploadedBy: match.createdBy || 'system',
                uploadedAt: match.createdAt || new Date(),
                isVerified: false
              }];
            }

            // 5. Check for uploadedScorecard field
            if (scorecardImages.length === 0 && match.uploadedScorecard) {
              scorecardImages = [{
                url: getFullImageUrl(match.uploadedScorecard),
                uploadedBy: match.createdBy || 'system',
                uploadedAt: match.createdAt || new Date(),
                isVerified: false
              }];
            }

            // 6. Check if there's a scorecard in match.result itself
            if (scorecardImages.length === 0 && match.result?.scorecard) {
              scorecardImages = [{
                url: getFullImageUrl(match.result.scorecard),
                uploadedBy: match.createdBy || 'system',
                uploadedAt: match.createdAt || new Date(),
                isVerified: false
              }];
            }

            // 7. Check for any image field that might contain the scorecard
            if (scorecardImages.length === 0) {
              const possibleImageFields = [
                match.scorecard,
                match.result?.image,
                match.image,
                match.result?.scorecardImage
              ];

              for (const imageUrl of possibleImageFields) {
                if (imageUrl && typeof imageUrl === 'string') {
                  scorecardImages = [{
                    url: getFullImageUrl(imageUrl),
                    uploadedBy: match.createdBy || 'system',
                    uploadedAt: match.createdAt || new Date(),
                    isVerified: false
                  }];
                  break;
                }
              }
            }

            // Debug logging
            console.log('Match data for scorecard images:', {
              'result.scorecardImages': match.result?.scorecardImages,
              'match.scorecardImages': match.scorecardImages,
              'ocrData.scorecardUrl': match.ocrData?.scorecardUrl,
              'match.scorecardImage': match.scorecardImage, // Added this key field
              'uploadedScorecard': match.uploadedScorecard,
              'result.scorecard': match.result?.scorecard,
              'result.image': match.result?.image,
              'match.image': match.image,
              'result.scorecardImage': match.result?.scorecardImage,
              'finalScorecardImages': scorecardImages
            });

            // Log detailed scorecard images if they exist
            if (match.result?.scorecardImages && match.result.scorecardImages.length > 0) {
              console.log('Detailed result.scorecardImages:', match.result.scorecardImages);
              match.result.scorecardImages.forEach((img, index) => {
                console.log(`Scorecard image ${index}:`, {
                  url: img.url,
                  fullUrl: getFullImageUrl(img.url),
                  uploadedBy: img.uploadedBy,
                  uploadedAt: img.uploadedAt,
                  isVerified: img.isVerified
                });
              });
            }

            return scorecardImages.length > 0 ? (
              <Paper sx={{ p: 2, m: 2, borderRadius: 2 }}>
                <Typography variant="h6" gutterBottom sx={{ textAlign: 'center', mb: 2 }}>
                  📊 Match Scorecard
                </Typography>
                <Box sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  minHeight: '300px',
                  bgcolor: 'grey.50',
                  borderRadius: 2,
                  border: '2px dashed',
                  borderColor: 'grey.300'
                }}>
                  <Card sx={{
                    cursor: 'pointer',
                    transition: 'transform 0.2s',
                    maxWidth: '100%',
                    maxHeight: '400px',
                    '&:hover': {
                      transform: 'scale(1.02)',
                      boxShadow: 3
                    }
                  }}>
                    <CardMedia
                      component="img"
                      sx={{
                        maxHeight: '400px',
                        width: 'auto',
                        objectFit: 'contain'
                      }}
                      image={scorecardImages[0].url}
                      alt="Match Scorecard"
                      onClick={() => {
                        setSelectedImage(scorecardImages[0].url);
                        setImageDialogOpen(true);
                      }}
                    />
                  </Card>
                </Box>
                {scorecardImages.length > 1 && (
                  <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', mt: 1 }}>
                    +{scorecardImages.length - 1} more images available
                  </Typography>
                )}
              </Paper>
            ) : (
              <Paper sx={{ p: 4, m: 2, borderRadius: 2, bgcolor: 'grey.50', textAlign: 'center' }}>
                <Typography variant="h6" color="text.secondary">
                  No Scorecard Image Available
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Scorecard image will be displayed here when available
                </Typography>
                <Typography variant="caption" color="text.disabled" sx={{ mt: 2, display: 'block' }}>
                  Debug: Check browser console for detailed image search results
                </Typography>
              </Paper>
            );
          })()}

          {/* Teams vs Teams Section - Side by Side */}
          <Box sx={{ display: 'flex', gap: 2, m: 2 }}>
            {(() => {
              // Determine batting order
              const homeTeamBattedFirst = match.homeTeamBattedFirst;
              const battingFirstTeam = homeTeamBattedFirst ? match.homeTeam : match.awayTeam;
              const chasingTeam = homeTeamBattedFirst ? match.awayTeam : match.homeTeam;
              const battingFirstScore = homeTeamBattedFirst ? match.result?.homeTeamScore : match.result?.awayTeamScore;
              const chasingScore = homeTeamBattedFirst ? match.result?.awayTeamScore : match.result?.homeTeamScore;

              return (
                <>
                  {/* Team Batted First */}
                  <Paper sx={{
                    flex: 1,
                    p: 3,
                    borderRadius: 2,
                    border: '2px solid',
                    borderColor: 'primary.light',
                    bgcolor: 'primary.50',
                    textAlign: 'center'
                  }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2 }}>
                      <Avatar
                        src={battingFirstTeam.logo}
                        alt={battingFirstTeam.teamName}
                        sx={{ width: 40, height: 40, mr: 2 }}
                      />
                      <Box>
                        <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                          {battingFirstTeam.teamName}
                        </Typography>
                        <Typography variant="caption" color="primary.main" sx={{ fontWeight: 'bold' }}>
                          Batted First • {battingFirstTeam._id.toString() === match.homeTeam._id.toString() ? 'Home' : 'Away'}
                        </Typography>
                      </Box>
                      {match.result?.winner && match.result.winner.toString() === battingFirstTeam._id.toString() && (
                        <Chip label="🏆" color="success" size="small" sx={{ ml: 1 }} />
                      )}
                    </Box>

                    <Typography variant="h3" sx={{ fontWeight: 'bold', color: 'primary.dark', mb: 1 }}>
                      {battingFirstScore?.runs || 0}/{battingFirstScore?.wickets || 0}
                    </Typography>

                    <Typography variant="body1" color="text.secondary">
                      Overs: {battingFirstScore?.overs || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Extras: {battingFirstScore?.extras || 0}
                    </Typography>
                  </Paper>

                  {/* VS Indicator */}
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    minWidth: '80px'
                  }}>
                    <Typography variant="h2" sx={{ fontWeight: 'bold', color: 'grey.600' }}>
                      VS
                    </Typography>
                  </Box>

                  {/* Team Batted Second */}
                  <Paper sx={{
                    flex: 1,
                    p: 3,
                    borderRadius: 2,
                    border: '2px solid',
                    borderColor: 'secondary.light',
                    bgcolor: 'secondary.50',
                    textAlign: 'center'
                  }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2 }}>
                      <Avatar
                        src={chasingTeam.logo}
                        alt={chasingTeam.teamName}
                        sx={{ width: 40, height: 40, mr: 2 }}
                      />
                      <Box>
                        <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                          {chasingTeam.teamName}
                        </Typography>
                        <Typography variant="caption" color="secondary.main" sx={{ fontWeight: 'bold' }}>
                          Batted Second • {chasingTeam._id.toString() === match.homeTeam._id.toString() ? 'Home' : 'Away'}
                        </Typography>
                      </Box>
                      {match.result?.winner && match.result.winner.toString() === chasingTeam._id.toString() && (
                        <Chip label="🏆" color="success" size="small" sx={{ ml: 1 }} />
                      )}
                    </Box>

                    <Typography variant="h3" sx={{ fontWeight: 'bold', color: 'secondary.dark', mb: 1 }}>
                      {chasingScore?.runs || 0}/{chasingScore?.wickets || 0}
                    </Typography>

                    <Typography variant="body1" color="text.secondary">
                      Overs: {chasingScore?.overs || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Extras: {chasingScore?.extras || 0}
                    </Typography>
                  </Paper>
                </>
              );
            })()}
          </Box>

          {/* Match Result Description - Full Width Bottom */}
          {match.result && (
            <Paper sx={{
              p: 4,
              m: 2,
              bgcolor: 'success.light',
              border: '2px solid',
              borderColor: 'success.main',
              borderRadius: 2,
              textAlign: 'center'
            }}>
              <Typography variant="h5" gutterBottom sx={{ color: 'success.dark', fontWeight: 'bold' }}>
                🏆 Match Result Description
              </Typography>
              <Typography
                variant="h4"
                sx={{
                  fontWeight: 'bold',
                  color: 'success.dark',
                  mb: 2
                }}
              >
                {match.result.description || 'Match completed'}
              </Typography>
              {match.result.winner && (() => {
                const winnerId =
                  typeof match.result.winner === 'string'
                    ? match.result.winner
                    : match.result.winner?._id || match.result.winner?.toString();
                const winnerTeam =
                  tournament.registeredTeams.find(
                    t => t._id.toString() === winnerId
                  ) || match.result.winner;
                const winnerName = winnerTeam?.teamName || 'Unknown Team';
                return (
                  <Typography variant="h6" sx={{ color: 'success.dark', fontStyle: 'italic' }}>
                    Winner: {winnerName}
                  </Typography>
                );
              })()}
              <Typography variant="body2" sx={{ mt: 2, color: 'success.dark', fontStyle: 'italic' }}>
                For Example: Chennai Super Kings Won By 8 Wickets etc.
              </Typography>
            </Paper>
          )}

          {/* Player Statistics Section - Side by Side */}
          {match.result?.playerPerformances && match.result.playerPerformances.length > 0 && (
            <Box sx={{ m: 2 }}>
              <Typography variant="h6" gutterBottom sx={{ textAlign: 'center', mb: 3 }}>
                🏏 Player Statistics (From OCR) - {match.result.playerPerformances.length} players
              </Typography>

              {(() => {
                // Group players by team and type - use OCR team order (Team 1, Team 2)
                const homeTeamId = match.homeTeam._id.toString();
                const awayTeamId = match.awayTeam._id.toString();

                // Determine which team is Team 1 and Team 2 based on team1IsHomeTeam flag
                const team1Id = match.team1IsHomeTeam ? homeTeamId : awayTeamId;
                const team2Id = match.team1IsHomeTeam ? awayTeamId : homeTeamId;
                const team1Name = match.team1IsHomeTeam ? match.homeTeam.teamName : match.awayTeam.teamName;
                const team2Name = match.team1IsHomeTeam ? match.awayTeam.teamName : match.homeTeam.teamName;

                const team1Players = match.result.playerPerformances.filter(p =>
                  p.team && p.team.toString() === team1Id
                );
                const team2Players = match.result.playerPerformances.filter(p =>
                  p.team && p.team.toString() === team2Id
                );

                // Separate batsmen and bowlers
                const team1Batsmen = team1Players.filter(p =>
                  p.batting && (p.batting.runs > 0 || p.batting.ballsFaced > 0)
                );
                const team1Bowlers = team1Players.filter(p =>
                  p.bowling && (p.bowling.wickets > 0 || p.bowling.runs > 0)
                );
                const team2Batsmen = team2Players.filter(p =>
                  p.batting && (p.batting.runs > 0 || p.batting.ballsFaced > 0)
                );
                const team2Bowlers = team2Players.filter(p =>
                  p.bowling && (p.bowling.wickets > 0 || p.bowling.runs > 0)
                );

                const renderPlayerTable = (players, type, teamName) => {
                  if (!players || players.length === 0) {
                    return (
                      <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 2 }}>
                        No {type} data available
                      </Typography>
                    );
                  }

                  return (
                    <TableContainer component={Paper} variant="outlined" sx={{ mt: 1 }}>
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell><strong>Player</strong></TableCell>
                            {type === 'batting' ? (
                              <>
                                <TableCell align="right"><strong>Runs</strong></TableCell>
                                <TableCell align="right"><strong>Balls</strong></TableCell>
                                <TableCell align="right"><strong>SR</strong></TableCell>
                              </>
                            ) : (
                              <>
                                <TableCell align="right"><strong>Runs</strong></TableCell>
                                <TableCell align="right"><strong>Wickets</strong></TableCell>
                              </>
                            )}
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {players.map((player, index) => {
                            const stats = type === 'batting' ? player.batting : player.bowling;
                            const strikeRate = type === 'batting' && stats.ballsFaced > 0
                              ? ((stats.runs / stats.ballsFaced) * 100).toFixed(1)
                              : '-';

                            return (
                              <TableRow key={index}>
                                <TableCell>
                                  <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                                    {player.playerName || 'Unknown Player'}
                                  </Typography>
                                </TableCell>
                                {type === 'batting' ? (
                                  <>
                                    <TableCell align="right">
                                      <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                                        {stats.runs || 0}{stats.notOut ? '*' : ''}
                                      </Typography>
                                    </TableCell>
                                    <TableCell align="right">{stats.ballsFaced || 0}</TableCell>
                                    <TableCell align="right">{strikeRate}</TableCell>
                                  </>
                                ) : (
                                  <>
                                    <TableCell align="right">{stats.runs || 0}</TableCell>
                                    <TableCell align="right">{stats.wickets || 0}</TableCell>
                                  </>
                                )}
                              </TableRow>
                            );
                          })}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  );
                };

                return (
                  <Box sx={{ display: 'flex', gap: 2 }}>
                    {/* Team 1 Statistics */}
                    <Paper sx={{ flex: 1, p: 2, borderRadius: 2, border: '2px solid', borderColor: 'primary.light' }}>
                      <Typography variant="h6" sx={{ fontWeight: 'bold', textAlign: 'center', mb: 2, color: 'primary.main' }}>
                        {team1Name} Statistics
                      </Typography>

                      {/* Team 1 Batsmen */}
                      <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 1 }}>
                        🏏 Batsmen ({team1Batsmen.length} players)
                      </Typography>
                      {renderPlayerTable(team1Batsmen, 'batting', team1Name)}

                      {/* Team 1 Bowlers */}
                      <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 1, mt: 3 }}>
                        ⚾ Bowlers ({team1Bowlers.length} players)
                      </Typography>
                      {renderPlayerTable(team1Bowlers, 'bowling', team1Name)}
                    </Paper>

                    {/* Team 2 Statistics */}
                    <Paper sx={{ flex: 1, p: 2, borderRadius: 2, border: '2px solid', borderColor: 'secondary.light' }}>
                      <Typography variant="h6" sx={{ fontWeight: 'bold', textAlign: 'center', mb: 2, color: 'secondary.main' }}>
                        {team2Name} Statistics
                      </Typography>

                      {/* Team 2 Batsmen */}
                      <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 1 }}>
                        🏏 Batsmen ({team2Batsmen.length} players)
                      </Typography>
                      {renderPlayerTable(team2Batsmen, 'batting', team2Name)}

                      {/* Team 2 Bowlers */}
                      <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 1, mt: 3 }}>
                        ⚾ Bowlers ({team2Bowlers.length} players)
                      </Typography>
                      {renderPlayerTable(team2Bowlers, 'bowling', team2Name)}
                    </Paper>
                  </Box>
                );
              })()}
            </Box>
          )}

          {/* Match conditions */}
          <Paper sx={{ p: 2, m: 2, borderRadius: 2 }}>
            <Typography variant="subtitle1" gutterBottom>
              Match Conditions
            </Typography>

            <Box sx={{ display: 'flex', gap: 4, justifyContent: 'space-around', flexWrap: 'wrap' }}>
              <Typography variant="body2" color="text.secondary">
                Weather: <strong>{match.conditions?.weather || 'Not specified'}</strong>
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Pitch: <strong>{match.conditions?.pitch || 'Not specified'}</strong>
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Outfield: <strong>{match.conditions?.outfield || 'Not specified'}</strong>
              </Typography>
            </Box>
          </Paper>

          {/* Scorecards */}
          <Paper sx={{ p: 2, m: 2, borderRadius: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="subtitle1">
                Additional Scorecards
              </Typography>

              {isTeamOwner && match.status === 'completed' && (
                <>
                  <Button
                    variant="outlined"
                    startIcon={<UploadIcon />}
                    endIcon={<ArrowDropDownIcon />}
                    size="small"
                    onClick={handleUploadMenuOpen}
                  >
                    Upload Scorecard
                  </Button>
                  <Menu
                    anchorEl={uploadMenuAnchor}
                    open={Boolean(uploadMenuAnchor)}
                    onClose={handleUploadMenuClose}
                  >
                    <MenuItem onClick={handleCameraCapture}>
                      <CameraIcon fontSize="small" sx={{ mr: 1 }} />
                      Camera Capture
                    </MenuItem>
                    <MenuItem onClick={handleTraditionalUpload}>
                      <UploadIcon fontSize="small" sx={{ mr: 1 }} />
                      File Upload
                    </MenuItem>
                  </Menu>
                </>
              )}
            </Box>

            {(() => {
              // Get scorecard images from match result or match itself
              let scorecardImages = match.result?.scorecardImages || match.scorecardImages || [];

              // Also check if there's an uploaded scorecard from the match creation process
              // This would be stored in match.uploadedScorecard or similar field
              if (scorecardImages.length === 0 && match.uploadedScorecard) {
                scorecardImages = [{
                  url: match.uploadedScorecard,
                  uploadedBy: match.createdBy || 'system',
                  uploadedAt: match.createdAt || new Date(),
                  isVerified: false
                }];
              }

              // If still no images, check if there's a scorecard URL in the OCR data
              if (scorecardImages.length === 0 && match.ocrData?.scorecardUrl) {
                scorecardImages = [{
                  url: match.ocrData.scorecardUrl,
                  uploadedBy: match.createdBy || 'system',
                  uploadedAt: match.createdAt || new Date(),
                  isVerified: false
                }];
              }

              return scorecardImages.length > 0 ? (
                <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                  {scorecardImages.map((image, index) => (
                    <Card key={index} sx={{ minWidth: 200 }}>
                      <CardMedia
                        component="img"
                        height="140"
                        image={image.url}
                        alt={`Scorecard ${index + 1}`}
                        sx={{ cursor: 'pointer', objectFit: 'cover' }}
                        onClick={() => {
                          setSelectedImage(image.url);
                          setImageDialogOpen(true);
                        }}
                      />
                      <CardContent sx={{ py: 1 }}>
                        <Typography variant="body2" color="text.secondary">
                          Uploaded: {formatDate(image.uploadedAt)}
                        </Typography>
                      </CardContent>
                      <CardActions sx={{ justifyContent: 'space-between' }}>
                        <Chip
                          label={image.isVerified ? 'Verified' : 'Pending'}
                          color={image.isVerified ? 'success' : 'warning'}
                          size="small"
                        />
                        <IconButton
                          size="small"
                          onClick={() => {
                            setSelectedImage(image.url);
                            setImageDialogOpen(true);
                          }}
                        >
                          <ImageIcon fontSize="small" />
                        </IconButton>
                      </CardActions>
                    </Card>
                  ))}
                </Box>
              ) : (
                <Alert severity="info">
                  No additional scorecards have been uploaded for this match yet.
                  {isTeamOwner && match.status === 'completed' && (
                    <Box sx={{ mt: 1, display: 'flex', gap: 1 }}>
                      <Button
                        variant="outlined"
                        size="small"
                        startIcon={<CameraIcon />}
                        onClick={handleCameraCapture}
                        color="primary"
                      >
                        Camera Capture
                      </Button>
                      <Button
                        variant="outlined"
                        size="small"
                        startIcon={<UploadIcon />}
                        onClick={handleTraditionalUpload}
                      >
                        File Upload
                      </Button>
                    </Box>
                  )}
                </Alert>
              );
            })()}
          </Paper>

          {/* Match notes */}
          {match.result?.description && (
            <Paper sx={{ p: 2, m: 2, borderRadius: 2 }}>
              <Typography variant="subtitle1" gutterBottom>
                Match Notes
              </Typography>
              <Typography variant="body2">
                {match.result.description}
              </Typography>
            </Paper>
          )}

          {/* Dispute information */}
          {match.result?.dispute?.isDisputed && (
            <Paper sx={{ p: 2, m: 2, borderRadius: 2, bgcolor: '#fff4e5' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <FlagIcon color="warning" sx={{ mr: 1 }} />
                <Typography variant="subtitle1" color="warning.main">
                  Match Disputed
                </Typography>
              </Box>

              <Typography variant="body2" paragraph>
                <strong>Reason:</strong> {match.result.dispute.disputeReason}
              </Typography>

              <Typography variant="body2" color="text.secondary">
                Status: <Chip
                  label={match.result.dispute.disputeStatus}
                  color={match.result.dispute.disputeStatus === 'resolved' ? 'success' : 'warning'}
                  size="small"
                />
              </Typography>

              {match.result.dispute.disputeResolution && (
                <Typography variant="body2" sx={{ mt: 1 }}>
                  <strong>Resolution:</strong> {match.result.dispute.disputeResolution}
                </Typography>
              )}
            </Paper>
          )}
        </DialogContent>

        <DialogActions>
          <Button onClick={onClose}>
            Close
          </Button>

          {isTeamOwner && match.status === 'completed' && match.result?.verificationStatus === 'pending' && (
            <Button
              variant="outlined"
              color="warning"
              startIcon={<DisputeIcon />}
              onClick={() => setDisputeDialogOpen(true)}
              disabled={loading}
            >
              Dispute Result
            </Button>
          )}

          {isAdmin && match.status === 'completed' && match.result?.verificationStatus === 'pending' && (
            <Button
              variant="contained"
              color="success"
              startIcon={loading ? <CircularProgress size={20} /> : <VerifyIcon />}
              onClick={handleVerifyMatch}
              disabled={loading}
            >
              Verify Result
            </Button>
          )}
        </DialogActions>
      </Dialog>

      {/* Scorecard upload dialog */}
      <ScorecardUpload
        open={uploadDialogOpen}
        onClose={() => setUploadDialogOpen(false)}
        tournamentId={tournament?._id}
        matchId={match?._id}
        onUploadSuccess={handleUploadSuccess}
        tournament={tournament} // Pass the full tournament object
      />

      {/* Dispute dialog */}
      <Dialog open={disputeDialogOpen} onClose={() => setDisputeDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <DisputeIcon sx={{ mr: 1, color: 'warning.main' }} />
            <Typography variant="h6">Dispute Match Result</Typography>
          </Box>
        </DialogTitle>

        <DialogContent>
          <Typography variant="body2" paragraph>
            Please provide a reason for disputing this match result. This will notify the tournament administrator.
          </Typography>

          <TextField
            fullWidth
            label="Dispute Reason"
            multiline
            rows={4}
            value={disputeReason}
            onChange={(e) => setDisputeReason(e.target.value)}
            placeholder="Explain why you are disputing this match result..."
            required
          />
        </DialogContent>

        <DialogActions>
          <Button onClick={() => setDisputeDialogOpen(false)} disabled={loading}>
            Cancel
          </Button>
          <Button
            variant="contained"
            color="warning"
            onClick={handleDisputeMatch}
            disabled={loading || !disputeReason.trim()}
            startIcon={loading ? <CircularProgress size={20} /> : <FlagIcon />}
          >
            Submit Dispute
          </Button>
        </DialogActions>
      </Dialog>

      {/* Image preview dialog */}
      <Dialog open={imageDialogOpen} onClose={() => setImageDialogOpen(false)} maxWidth="lg">
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography variant="h6">Scorecard Image</Typography>
            <IconButton onClick={() => setImageDialogOpen(false)}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>

        <DialogContent>
          {selectedImage && (
            <img
              src={selectedImage}
              alt="Scorecard"
              style={{
                maxWidth: '100%',
                maxHeight: '80vh',
                display: 'block',
                margin: '0 auto'
              }}
            />
          )}
        </DialogContent>
      </Dialog>
    </>
  );
};

export default MatchDetail;
