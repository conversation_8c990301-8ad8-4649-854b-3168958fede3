import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Grid,
  Divider,
  Chip,
  Avatar,
  IconButton,
  Paper,
  Alert,
  CircularProgress,
  Tooltip,
  Card,
  CardMedia,
  CardContent,
  CardActions,
  TextField,
  Menu,
  MenuItem
} from '@mui/material';
import {
  Close as CloseIcon,
  CloudUpload as UploadIcon,
  CheckCircle as VerifyIcon,
  Warning as DisputeIcon,
  Flag as FlagIcon,
  Image as ImageIcon,
  SportsCricket as CricketIcon,
  CameraAlt as CameraIcon,
  ArrowDropDown as ArrowDropDownIcon
} from '@mui/icons-material';
import { format } from 'date-fns';
import { useAuth } from '../../hooks/useAuth';
import { verifyMatch, disputeMatch } from '../../services/scorecardService';
import ScorecardUpload from './ScorecardUpload';
import { useNavigate } from 'react-router-dom';

const MatchDetail = ({ open, onClose, match, tournament, onActionComplete }) => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [disputeDialogOpen, setDisputeDialogOpen] = useState(false);
  const [disputeReason, setDisputeReason] = useState('');
  const [selectedImage, setSelectedImage] = useState(null);
  const [imageDialogOpen, setImageDialogOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [uploadMenuAnchor, setUploadMenuAnchor] = useState(null);

  if (!match) return null;

  // Format date
  const formatDate = (date) => {
    if (!date) return 'TBD';
    return format(new Date(date), 'MMM dd, yyyy h:mm a');
  };

  // Check if user is admin
  const isAdmin = user && user.role === 'admin';

  // Check if user is team owner of one of the teams
  const isTeamOwner = user && user.role === 'team_owner' && user.team &&
    (match.homeTeam._id === user.team || match.awayTeam._id === user.team);

  // Handle upload menu open
  const handleUploadMenuOpen = (event) => {
    setUploadMenuAnchor(event.currentTarget);
  };

  // Handle upload menu close
  const handleUploadMenuClose = () => {
    setUploadMenuAnchor(null);
  };

  // Handle camera capture
  const handleCameraCapture = () => {
    handleUploadMenuClose();
    // Store match and tournament info in session storage
    sessionStorage.setItem('captureContext', JSON.stringify({
      tournamentId: tournament._id,
      matchId: match._id,
      returnPath: `/tournaments/${tournament._id}`
    }));
    // Navigate to camera capture
    navigate('/scorecard-capture');
  };

  // Handle traditional upload
  const handleTraditionalUpload = () => {
    handleUploadMenuClose();
    setUploadDialogOpen(true);
  };

  // Handle scorecard upload success
  const handleUploadSuccess = (result) => {
    setSuccess('Scorecard uploaded successfully');
    if (onActionComplete) {
      onActionComplete();
    }
  };

  // Handle verify match
  const handleVerifyMatch = async () => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);

      await verifyMatch(tournament._id, match._id);

      setSuccess('Match verified successfully');

      if (onActionComplete) {
        onActionComplete();
      }
    } catch (err) {
      console.error('Error verifying match:', err);
      setError(err.message || 'Failed to verify match. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle dispute match
  const handleDisputeMatch = async () => {
    if (!disputeReason.trim()) {
      setError('Please provide a reason for the dispute');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      setSuccess(null);

      await disputeMatch(tournament._id, match._id, disputeReason);

      setSuccess('Match disputed successfully');
      setDisputeDialogOpen(false);

      if (onActionComplete) {
        onActionComplete();
      }
    } catch (err) {
      console.error('Error disputing match:', err);
      setError(err.message || 'Failed to dispute match. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'in_progress':
        return 'warning';
      case 'cancelled':
        return 'error';
      default:
        return 'default';
    }
  };

  // Get verification status color
  const getVerificationStatusColor = (status) => {
    switch (status) {
      case 'verified':
        return 'success';
      case 'disputed':
        return 'error';
      case 'resolved':
        return 'info';
      default:
        return 'warning';
    }
  };

  // Get verification status label
  const getVerificationStatusLabel = (status) => {
    switch (status) {
      case 'verified':
        return 'Verified';
      case 'disputed':
        return 'Disputed';
      case 'resolved':
        return 'Resolved';
      default:
        return 'Pending Verification';
    }
  };

  return (
    <>
      <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <CricketIcon sx={{ mr: 1 }} />
              <Typography variant="h6">Match Details</Typography>
            </Box>
            <IconButton onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>

        <DialogContent>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {success && (
            <Alert severity="success" sx={{ mb: 2 }}>
              {success}
            </Alert>
          )}

          <Grid container spacing={3}>
            {/* Match header */}
            <Grid item xs={12}>
              <Paper sx={{ p: 2, mb: 2 }}>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} sm={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Typography variant="subtitle1">
                        {formatDate(match.date)}
                      </Typography>
                      <Chip
                        label={match.status}
                        color={getStatusColor(match.status)}
                        size="small"
                        sx={{ ml: 1 }}
                      />
                      {match.result?.verificationStatus && (
                        <Chip
                          label={getVerificationStatusLabel(match.result.verificationStatus)}
                          color={getVerificationStatusColor(match.result.verificationStatus)}
                          size="small"
                          sx={{ ml: 1 }}
                        />
                      )}
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      {match.venue || 'Venue not specified'}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6} sx={{ textAlign: { xs: 'left', sm: 'right' } }}>
                    <Typography variant="body2" color="text.secondary">
                      Format: <strong>{match.format}</strong>
                    </Typography>
                    {match.result?.isTie && (
                      <Chip label="Match Tied" size="small" color="info" sx={{ mt: 1 }} />
                    )}
                  </Grid>
                </Grid>
              </Paper>
            </Grid>

            {/* Scorecard Image Section */}
            {(() => {
              // Get scorecard images
              let scorecardImages = [];

              // Check for images in result.scorecardImages
              if (match.result?.scorecardImages && Array.isArray(match.result.scorecardImages)) {
                scorecardImages = match.result.scorecardImages;
              }

              // If no images in result, check match.scorecardImages
              if (scorecardImages.length === 0 && match.scorecardImages && Array.isArray(match.scorecardImages)) {
                scorecardImages = match.scorecardImages;
              }

              // If still no images, check if there's a scorecard URL in the OCR data
              if (scorecardImages.length === 0 && match.ocrData?.scorecardUrl) {
                scorecardImages = [{
                  url: match.ocrData.scorecardUrl,
                  uploadedBy: match.createdBy || 'system',
                  uploadedAt: match.createdAt || new Date(),
                  isVerified: false
                }];
              }

              return scorecardImages.length > 0 ? (
                <Grid item xs={12}>
                  <Paper sx={{ p: 2, mb: 2 }}>
                    <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                      📊 Scorecard
                      <Chip
                        label={`${scorecardImages.length} image${scorecardImages.length > 1 ? 's' : ''}`}
                        size="small"
                        sx={{ ml: 1 }}
                      />
                    </Typography>
                    <Grid container spacing={2}>
                      {scorecardImages.slice(0, 3).map((image, index) => (
                        <Grid item xs={12} sm={6} md={4} key={index}>
                          <Card sx={{
                            cursor: 'pointer',
                            transition: 'transform 0.2s',
                            '&:hover': {
                              transform: 'scale(1.02)',
                              boxShadow: 3
                            }
                          }}>
                            <CardMedia
                              component="img"
                              height="120"
                              image={image.url}
                              alt={`Scorecard ${index + 1}`}
                              sx={{ objectFit: 'cover' }}
                              onClick={() => {
                                setSelectedImage(image.url);
                                setImageDialogOpen(true);
                              }}
                            />
                            <Box sx={{ p: 1 }}>
                              <Typography variant="caption" color="text.secondary">
                                Click to view full size
                              </Typography>
                            </Box>
                          </Card>
                        </Grid>
                      ))}
                      {scorecardImages.length > 3 && (
                        <Grid item xs={12}>
                          <Typography variant="body2" color="text.secondary">
                            +{scorecardImages.length - 3} more images available in the Scorecards section below
                          </Typography>
                        </Grid>
                      )}
                    </Grid>
                  </Paper>
                </Grid>
              ) : null;
            })()}

            {/* Teams and scores - Display in batting order */}
            {(() => {
              // Determine batting order
              const homeTeamBattedFirst = match.homeTeamBattedFirst;
              const battingFirstTeam = homeTeamBattedFirst ? match.homeTeam : match.awayTeam;
              const chasingTeam = homeTeamBattedFirst ? match.awayTeam : match.homeTeam;
              const battingFirstScore = homeTeamBattedFirst ? match.result?.homeTeamScore : match.result?.awayTeamScore;
              const chasingScore = homeTeamBattedFirst ? match.result?.awayTeamScore : match.result?.homeTeamScore;

              return (
                <>
                  {/* Batting First Team */}
                  <Grid item xs={12} md={5}>
                    <Paper sx={{
                      p: 2,
                      height: '100%',
                      border: '2px solid',
                      borderColor: 'primary.light',
                      bgcolor: 'primary.50'
                    }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <Avatar src={battingFirstTeam.logo} alt={battingFirstTeam.teamName} sx={{ mr: 1 }} />
                        <Box sx={{ flexGrow: 1 }}>
                          <Typography variant="h6">
                            {battingFirstTeam.teamName}
                          </Typography>
                          <Typography variant="caption" color="primary.main" sx={{ fontWeight: 'bold' }}>
                            Batted First • {battingFirstTeam._id.toString() === match.homeTeam._id.toString() ? 'Home' : 'Away'}
                          </Typography>
                        </Box>
                        {match.result?.winner && match.result.winner.toString() === battingFirstTeam._id.toString() && (
                          <Chip label="Winner" color="success" size="small" />
                        )}
                      </Box>

                      <Divider sx={{ mb: 2 }} />

                      <Typography variant="h4" gutterBottom color="primary.dark">
                        {battingFirstScore?.runs || 0}/{battingFirstScore?.wickets || 0}
                      </Typography>

                      <Typography variant="body2" color="text.secondary">
                        Overs: {battingFirstScore?.overs || 0}
                      </Typography>

                      <Typography variant="body2" color="text.secondary">
                        Extras: {battingFirstScore?.extras || 0}
                      </Typography>
                    </Paper>
                  </Grid>

                  {/* VS Indicator */}
                  <Grid item xs={12} md={2}>
                    <Box sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      height: '100%',
                      minHeight: '200px'
                    }}>
                      <Paper sx={{
                        p: 2,
                        bgcolor: 'grey.100',
                        border: '2px solid',
                        borderColor: 'grey.300',
                        borderRadius: '50%',
                        width: '80px',
                        height: '80px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                      }}>
                        <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'grey.700' }}>
                          VS
                        </Typography>
                      </Paper>
                    </Box>
                  </Grid>

                  {/* Chasing Team */}
                  <Grid item xs={12} md={5}>
                    <Paper sx={{
                      p: 2,
                      height: '100%',
                      border: '2px solid',
                      borderColor: 'secondary.light',
                      bgcolor: 'secondary.50'
                    }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <Avatar src={chasingTeam.logo} alt={chasingTeam.teamName} sx={{ mr: 1 }} />
                        <Box sx={{ flexGrow: 1 }}>
                          <Typography variant="h6">
                            {chasingTeam.teamName}
                          </Typography>
                          <Typography variant="caption" color="secondary.main" sx={{ fontWeight: 'bold' }}>
                            Chased • {chasingTeam._id.toString() === match.homeTeam._id.toString() ? 'Home' : 'Away'}
                          </Typography>
                        </Box>
                        {match.result?.winner && match.result.winner.toString() === chasingTeam._id.toString() && (
                          <Chip label="Winner" color="success" size="small" />
                        )}
                      </Box>

                      <Divider sx={{ mb: 2 }} />

                      <Typography variant="h4" gutterBottom color="secondary.dark">
                        {chasingScore?.runs || 0}/{chasingScore?.wickets || 0}
                      </Typography>

                      <Typography variant="body2" color="text.secondary">
                        Overs: {chasingScore?.overs || 0}
                      </Typography>

                      <Typography variant="body2" color="text.secondary">
                        Extras: {chasingScore?.extras || 0}
                      </Typography>
                    </Paper>
                  </Grid>
                </>
              );
            })()}

            {/* Match Result */}
{match.result && (
  <Grid item xs={12}>
    <Paper sx={{ 
      p: 3, 
      bgcolor: 'success.light', 
      border: '2px solid',
      borderColor: 'success.main',
      borderRadius: 2
    }}>
      <Typography variant="h6" gutterBottom sx={{ color: 'success.dark', fontWeight: 'bold' }}>
        🏆 Match Result
      </Typography>
      <Typography 
        variant="h5" 
        sx={{ 
          fontWeight: 'bold',
          color: 'success.dark',
          textAlign: 'center',
          mt: 1
        }}
      >
        {match.result.description || 'Match completed'}
      </Typography>
      {match.result.winner && (() => {
        const winnerId =
          typeof match.result.winner === 'string'
            ? match.result.winner
            : match.result.winner?._id || match.result.winner?.toString();
        const winnerTeam =
          tournament.registeredTeams.find(
            t => t._id.toString() === winnerId
          ) || match.result.winner;
        const winnerName = winnerTeam?.teamName || 'Unknown Team';
        return (
          <Typography variant="body1" sx={{ textAlign: 'center', mt: 1 }}>
            Winner: {winnerName}
          </Typography>
        );
      })()}
    </Paper>
  </Grid>
)}

            {/* Match conditions */}
            <Grid item xs={12}>
              <Paper sx={{ p: 2 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Match Conditions
                </Typography>

                <Grid container spacing={2}>
                  <Grid item xs={4}>
                    <Typography variant="body2" color="text.secondary">
                      Weather: <strong>{match.conditions?.weather || 'Not specified'}</strong>
                    </Typography>
                  </Grid>

                  <Grid item xs={4}>
                    <Typography variant="body2" color="text.secondary">
                      Pitch: <strong>{match.conditions?.pitch || 'Not specified'}</strong>
                    </Typography>
                  </Grid>

                  <Grid item xs={4}>
                    <Typography variant="body2" color="text.secondary">
                      Outfield: <strong>{match.conditions?.outfield || 'Not specified'}</strong>
                    </Typography>
                  </Grid>
                </Grid>
              </Paper>
            </Grid>

            {/* Scorecards */}
            <Grid item xs={12}>
              <Paper sx={{ p: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="subtitle1">
                    Scorecards
                  </Typography>

                  {isTeamOwner && match.status === 'completed' && (
                    <>
                      <Button
                        variant="outlined"
                        startIcon={<UploadIcon />}
                        endIcon={<ArrowDropDownIcon />}
                        size="small"
                        onClick={handleUploadMenuOpen}
                      >
                        Upload Scorecard
                      </Button>
                      <Menu
                        anchorEl={uploadMenuAnchor}
                        open={Boolean(uploadMenuAnchor)}
                        onClose={handleUploadMenuClose}
                      >
                        <MenuItem onClick={handleCameraCapture}>
                          <CameraIcon fontSize="small" sx={{ mr: 1 }} />
                          Camera Capture
                        </MenuItem>
                        <MenuItem onClick={handleTraditionalUpload}>
                          <UploadIcon fontSize="small" sx={{ mr: 1 }} />
                          File Upload
                        </MenuItem>
                      </Menu>
                    </>
                  )}
                </Box>

                {(() => {
                  // Get scorecard images from match result or match itself
                  let scorecardImages = match.result?.scorecardImages || match.scorecardImages || [];
                  
                  // Also check if there's an uploaded scorecard from the match creation process
                  // This would be stored in match.uploadedScorecard or similar field
                  if (scorecardImages.length === 0 && match.uploadedScorecard) {
                    scorecardImages = [{
                      url: match.uploadedScorecard,
                      uploadedBy: match.createdBy || 'system',
                      uploadedAt: match.createdAt || new Date(),
                      isVerified: false
                    }];
                  }
                  
                  // If still no images, check if there's a scorecard URL in the OCR data
                  if (scorecardImages.length === 0 && match.ocrData?.scorecardUrl) {
                    scorecardImages = [{
                      url: match.ocrData.scorecardUrl,
                      uploadedBy: match.createdBy || 'system',
                      uploadedAt: match.createdAt || new Date(),
                      isVerified: false
                    }];
                  }
                  
                  return scorecardImages.length > 0 ? (
                    <Grid container spacing={2}>
                      {scorecardImages.map((image, index) => (
                      <Grid item xs={12} sm={6} md={4} key={index}>
                        <Card>
                          <CardMedia
                            component="img"
                            height="140"
                            image={image.url}
                            alt={`Scorecard ${index + 1}`}
                            sx={{ cursor: 'pointer', objectFit: 'cover' }}
                            onClick={() => {
                              setSelectedImage(image.url);
                              setImageDialogOpen(true);
                            }}
                          />
                          <CardContent sx={{ py: 1 }}>
                            <Typography variant="body2" color="text.secondary">
                              Uploaded: {formatDate(image.uploadedAt)}
                            </Typography>
                          </CardContent>
                          <CardActions sx={{ justifyContent: 'space-between' }}>
                            <Chip
                              label={image.isVerified ? 'Verified' : 'Pending'}
                              color={image.isVerified ? 'success' : 'warning'}
                              size="small"
                            />
                            <IconButton
                              size="small"
                              onClick={() => {
                                setSelectedImage(image.url);
                                setImageDialogOpen(true);
                              }}
                            >
                              <ImageIcon fontSize="small" />
                            </IconButton>
                          </CardActions>
                        </Card>
                      </Grid>
                    ))}
                    </Grid>
                  ) : (
                    <Alert severity="info">
                      No scorecards have been uploaded for this match yet.
                      {isTeamOwner && match.status === 'completed' && (
                        <Box sx={{ mt: 1, display: 'flex', gap: 1 }}>
                          <Button
                            variant="outlined"
                            size="small"
                            startIcon={<CameraIcon />}
                            onClick={handleCameraCapture}
                            color="primary"
                          >
                            Camera Capture
                          </Button>
                          <Button
                            variant="outlined"
                            size="small"
                            startIcon={<UploadIcon />}
                            onClick={handleTraditionalUpload}
                          >
                            File Upload
                          </Button>
                        </Box>
                      )}
                    </Alert>
                  );
                })()}
              </Paper>
            </Grid>

            {/* Match notes */}
            {match.result?.description && (
              <Grid item xs={12}>
                <Paper sx={{ p: 2 }}>
                  <Typography variant="subtitle1" gutterBottom>
                    Match Notes
                  </Typography>
                  <Typography variant="body2">
                    {match.result.description}
                  </Typography>
                </Paper>
              </Grid>
            )}

            {/* Dispute information */}
            {match.result?.dispute?.isDisputed && (
              <Grid item xs={12}>
                <Paper sx={{ p: 2, bgcolor: '#fff4e5' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <FlagIcon color="warning" sx={{ mr: 1 }} />
                    <Typography variant="subtitle1" color="warning.main">
                      Match Disputed
                    </Typography>
                  </Box>

                  <Typography variant="body2" paragraph>
                    <strong>Reason:</strong> {match.result.dispute.disputeReason}
                  </Typography>

                  <Typography variant="body2" color="text.secondary">
                    Status: <Chip
                      label={match.result.dispute.disputeStatus}
                      color={match.result.dispute.disputeStatus === 'resolved' ? 'success' : 'warning'}
                      size="small"
                    />
                  </Typography>

                  {match.result.dispute.disputeResolution && (
                    <Typography variant="body2" sx={{ mt: 1 }}>
                      <strong>Resolution:</strong> {match.result.dispute.disputeResolution}
                    </Typography>
                  )}
                </Paper>
              </Grid>
            )}
          </Grid>
        </DialogContent>

        <DialogActions>
          <Button onClick={onClose}>
            Close
          </Button>

          {isTeamOwner && match.status === 'completed' && match.result?.verificationStatus === 'pending' && (
            <Button
              variant="outlined"
              color="warning"
              startIcon={<DisputeIcon />}
              onClick={() => setDisputeDialogOpen(true)}
              disabled={loading}
            >
              Dispute Result
            </Button>
          )}

          {isAdmin && match.status === 'completed' && match.result?.verificationStatus === 'pending' && (
            <Button
              variant="contained"
              color="success"
              startIcon={loading ? <CircularProgress size={20} /> : <VerifyIcon />}
              onClick={handleVerifyMatch}
              disabled={loading}
            >
              Verify Result
            </Button>
          )}
        </DialogActions>
      </Dialog>

      {/* Scorecard upload dialog */}
      <ScorecardUpload
        open={uploadDialogOpen}
        onClose={() => setUploadDialogOpen(false)}
        tournamentId={tournament?._id}
        matchId={match?._id}
        onUploadSuccess={handleUploadSuccess}
        tournament={tournament} // Pass the full tournament object
      />

      {/* Dispute dialog */}
      <Dialog open={disputeDialogOpen} onClose={() => setDisputeDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <DisputeIcon sx={{ mr: 1, color: 'warning.main' }} />
            <Typography variant="h6">Dispute Match Result</Typography>
          </Box>
        </DialogTitle>

        <DialogContent>
          <Typography variant="body2" paragraph>
            Please provide a reason for disputing this match result. This will notify the tournament administrator.
          </Typography>

          <TextField
            fullWidth
            label="Dispute Reason"
            multiline
            rows={4}
            value={disputeReason}
            onChange={(e) => setDisputeReason(e.target.value)}
            placeholder="Explain why you are disputing this match result..."
            required
          />
        </DialogContent>

        <DialogActions>
          <Button onClick={() => setDisputeDialogOpen(false)} disabled={loading}>
            Cancel
          </Button>
          <Button
            variant="contained"
            color="warning"
            onClick={handleDisputeMatch}
            disabled={loading || !disputeReason.trim()}
            startIcon={loading ? <CircularProgress size={20} /> : <FlagIcon />}
          >
            Submit Dispute
          </Button>
        </DialogActions>
      </Dialog>

      {/* Image preview dialog */}
      <Dialog open={imageDialogOpen} onClose={() => setImageDialogOpen(false)} maxWidth="lg">
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography variant="h6">Scorecard Image</Typography>
            <IconButton onClick={() => setImageDialogOpen(false)}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>

        <DialogContent>
          {selectedImage && (
            <img
              src={selectedImage}
              alt="Scorecard"
              style={{
                maxWidth: '100%',
                maxHeight: '80vh',
                display: 'block',
                margin: '0 auto'
              }}
            />
          )}
        </DialogContent>
      </Dialog>
    </>
  );
};

export default MatchDetail;
